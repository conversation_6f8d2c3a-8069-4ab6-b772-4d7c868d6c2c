// /// Generated tag class for pet operations.
// /// This file is auto-generated. Do not modify manually.

import 'package:dio/dio.dart';
import 'package:openapi_generator/src/runtime/api_client_base.dart';
import 'package:openapi_generator/src/runtime/tag_base.dart';

/// Tag class for pet operations.
/// Provides access to all endpoints tagged with "pet".
class PetTag extends TagBase {
  /// Creates a new pet tag instance.
  PetTag({required ApiClientBase apiClient})
      : super(apiClient: apiClient, tagName: 'pet');

  /// Add a new pet to the store.
  /// Add a new pet to the store.
  Future<ApiResponse<dynamic>> addPet() async {
    final queryParams = <String, dynamic>{};
    final path = '/pet';
    return post(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Update an existing pet.
  /// Update an existing pet by Id.
  Future<ApiResponse<dynamic>> updatePet() async {
    final queryParams = <String, dynamic>{};
    final path = '/pet';
    return put(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Finds Pets by status.
  /// Multiple status values can be provided with comma separated strings.
  Future<ApiResponse<dynamic>> findPetsByStatus({String? status}) async {
    final queryParams = buildQueryParameters({'status': status});
    final path = '/pet/findByStatus';
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Finds Pets by tags.
  /// Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing.
  Future<ApiResponse<dynamic>> findPetsByTags({String? tags}) async {
    final queryParams = buildQueryParameters({'tags': tags});
    final path = '/pet/findByTags';
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Find pet by ID.
  /// Returns a single pet.
  Future<ApiResponse<dynamic>> getPetById({required String petid}) async {
    final pathParams = {'petId': petid};
    final queryParams = <String, dynamic>{};
    final path = buildPath('/pet/{petId}', pathParams);
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Updates a pet in the store with form data.
  /// Updates a pet resource based on the form data.
  Future<ApiResponse<dynamic>> updatePetWithForm({
    required String petid,
    String? name,
    String? status,
  }) async {
    final pathParams = {'petId': petid};
    final queryParams = buildQueryParameters({'name': name, 'status': status});
    final path = buildPath('/pet/{petId}', pathParams);
    return post(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Deletes a pet.
  /// Delete a pet.
  Future<ApiResponse<dynamic>> deletePet({required String petid}) async {
    final pathParams = {'petId': petid};
    final queryParams = <String, dynamic>{};
    final path = buildPath('/pet/{petId}', pathParams);
    return delete(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Uploads an image.
  /// Upload image of the pet.
  Future<ApiResponse<dynamic>> uploadFile({
    required String petid,
    String? additionalmetadata,
  }) async {
    final pathParams = {'petId': petid};
    final queryParams =
        buildQueryParameters({'additionalMetadata': additionalmetadata});
    final path = buildPath('/pet/{petId}/uploadImage', pathParams);
    return post(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }
}
