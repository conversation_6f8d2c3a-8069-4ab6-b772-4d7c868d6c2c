import 'package:openapi_generator/openapi_generator.dart';
import 'package:test/test.dart';

void main() {
  group('OpenAPI Parser Tests', () {
    test('should parse valid OpenAPI spec from JSON string', () {
      const jsonSpec = '''
      {
        "openapi": "3.0.0",
        "info": {
          "title": "Test API",
          "version": "1.0.0"
        },
        "paths": {
          "/test": {
            "get": {
              "tags": ["test"],
              "operationId": "getTest",
              "responses": {
                "200": {
                  "description": "Success"
                }
              }
            }
          }
        }
      }
      ''';

      final spec = OpenApiParser.parseFromJsonString(jsonSpec);
      expect(spec.info.title, equals('Test API'));
      expect(spec.info.version, equals('1.0.0'));
      expect(spec.paths.containsKey('/test'), isTrue);
    });

    test('should extract tags from OpenAPI spec', () {
      const jsonSpec = '''
      {
        "openapi": "3.0.0",
        "info": {
          "title": "Test API",
          "version": "1.0.0"
        },
        "tags": [
          {"name": "users"},
          {"name": "posts"}
        ],
        "paths": {
          "/users": {
            "get": {
              "tags": ["users"],
              "responses": {"200": {"description": "Success"}}
            }
          },
          "/posts": {
            "get": {
              "tags": ["posts"],
              "responses": {"200": {"description": "Success"}}
            }
          }
        }
      }
      ''';

      final spec = OpenApiParser.parseFromJsonString(jsonSpec);
      final tags = spec.getAllTags();
      expect(tags, containsAll(['users', 'posts']));
    });
  });

  group('Code Generator Tests', () {
    test('should create generation result', () {
      final result = GenerationResult();
      expect(result.success, isFalse);
      expect(result.generatedFiles, isEmpty);
    });
  });
}
