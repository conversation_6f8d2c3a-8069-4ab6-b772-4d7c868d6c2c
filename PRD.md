# Product Requirements Document (PRD)
## Dart OpenAPI Code Generator

### 1. Executive Summary

**Product Name:** dart_openapi_generator (working name)

**Vision:** A comprehensive Dart package that automatically generates type-safe API client code from OpenAPI specifications, leveraging the Dio HTTP client for network operations.

**Problem Statement:** Developers working with APIs in Dart/Flutter applications often need to manually write boilerplate code for API calls, data models, and serialization. This is time-consuming, error-prone, and difficult to maintain when APIs evolve.

**Solution:** An automated code generation tool that parses OpenAPI specifications and generates production-ready Dart code including models, API clients, and HTTP handlers using Dio.

### 2. Product Objectives

**Primary Goals:**
- Eliminate manual API client code writing
- Ensure type safety across all API interactions
- Reduce development time by 60-80% for API integration
- Maintain consistency with API specification changes
- Provide seamless integration with existing Dart/Flutter projects

**Success Metrics:**
- Package adoption rate in pub.dev
- Developer time savings (measured through surveys)
- Code quality improvements (reduced API-related bugs)
- Community contributions and feedback

### 3. Target Audience

**Primary Users:**
- Flutter mobile developers
- Dart backend developers
- API-first development teams
- Developers working with microservices architectures

**User Personas:**
- **Mobile Developer Maya:** Needs to integrate multiple REST APIs into Flutter apps quickly
- **Backend Developer Ben:** Creates Dart servers that consume external APIs
- **Team Lead Lisa:** Wants consistent API integration patterns across team projects

### 4. Core Features & Requirements

#### 4.1 OpenAPI Specification Support
**Must Have:**
- OpenAPI 3.0.x specification parsing
- Multiple input sources:
  - Local YAML files
  - Local JSON files
  - Remote URLs (HTTP/HTTPS)
  - File system paths
- REST API endpoint definitions
- Request/response schema definitions
- Server-Sent Events (SSE) endpoint support
- Comprehensive authentication schemes:
  - Basic Authentication (username/password)
  - Bearer Token Authentication (JWT, API tokens)
  - API Key Authentication (header, query, cookie)
  - Multiple authentication schemes per specification

**Should Have:**
- OpenAPI 2.0 (Swagger) backward compatibility
- OAuth2 flows (Authorization Code, Client Credentials, etc.)
- Custom authentication headers and schemes
- Authentication scheme inheritance and overrides
- URL validation and HTTPS enforcement for remote specs
- Caching of remote specifications

**Could Have:**
- OpenAPI 3.1.x support
- Custom authentication interceptors
- Token refresh mechanisms
- Specification versioning and change detection

#### 4.2 Code Generation Capabilities
**Must Have:**
- Hierarchical API client structure: `APIClient().tagName.endpointName(...)`
- Tag-based endpoint grouping from OpenAPI specification
- REST endpoint method generation
- Server-Sent Events (SSE) stream method generation
- Dart model classes with proper serialization (json_annotation)
- Type-safe method signatures for each endpoint
- Request/response DTOs
- Enum generation for defined values
- Null safety compliance
- Documentation comments from OpenAPI descriptions

**Should Have:**
- Custom naming conventions configuration
- Optional/required parameter handling
- Generic response wrappers
- Error handling patterns
- Validation annotations
- Lazy initialization of tag groups
- SSE event filtering and transformation
- Stream reconnection handling

**Could Have:**
- Custom templates for code generation
- Plugin architecture for extensions
- IDE integration (VS Code, IntelliJ)
- Auto-completion support for the hierarchical structure
- SSE connection pooling and management

#### 4.3 Dio Integration & Authentication
**Must Have:**
- Dio HTTP client as the underlying network layer
- Native Dart SSE client for Server-Sent Events
- Configurable base URLs and headers
- Built-in authentication interceptors for all supported schemes:
  - Basic Auth interceptor with username/password encoding
  - Bearer token interceptor with automatic header injection
  - API key interceptor with flexible placement (header/query/cookie)
- Per-endpoint authentication override capabilities
- Request/response interceptors support
- SSE authentication and header injection
- Timeout configuration
- Comprehensive error handling with auth-specific error codes

**Should Have:**
- Token refresh mechanisms with automatic retry
- Secure token storage integration
- Connection pooling configuration
- Retry mechanisms with auth-aware logic
- Request/response logging with auth data masking
- Certificate pinning support
- Multi-auth scheme support (fallback mechanisms)
- SSE reconnection strategies with exponential backoff
- SSE event parsing and deserialization

**Could Have:**
- OAuth2 PKCE flow implementation
- Biometric authentication integration
- Custom authentication strategy plugins
- Performance monitoring integration
- Advanced caching strategies with auth context
- SSE connection multiplexing
- Real-time connection health monitoring

#### 4.4 Developer Experience
**Must Have:**
- Command-line interface (CLI) tool with multiple input options:
  - `dart_openapi_gen --input ./spec.yaml --output ./lib/api/`
  - `dart_openapi_gen --url https://api.example.com/openapi.json --output ./lib/api/`
  - `dart_openapi_gen --file ./specs/api.json --output ./lib/api/`
- Clear error messages and validation
- Comprehensive documentation
- Example projects and tutorials

**Should Have:**
- Build runner integration
- Watch mode for development with file monitoring
- Configuration file support (yaml/json)
- IDE plugins/extensions
- URL authentication for protected OpenAPI specs
- Batch processing for multiple specifications

**Could Have:**
- Web-based configuration tool
- Integration with popular CI/CD platforms
- Visual API explorer
- Specification diff and migration tools

### 5. Technical Requirements

#### 5.1 Architecture
- **Language:** Dart 3.0+
- **Dependencies:** 
  - dio: ^5.0.0
  - json_annotation: ^4.8.0
  - build_runner: ^2.4.0
  - yaml: ^3.1.0
- **Architecture Pattern:** Plugin-based code generation
- **Output:** Pure Dart code with minimal dependencies

#### 5.2 Performance Requirements
- Generate code for 100+ endpoints in under 10 seconds
- Support OpenAPI files up to 10MB
- Memory usage under 256MB during generation
- Generated code should have minimal runtime overhead

#### 5.3 Compatibility
- Dart SDK: 3.0.0 or higher
- Flutter: 3.10.0 or higher
- Platform support: All Dart-supported platforms
- IDE support: VS Code, IntelliJ IDEA, Android Studio

### 6. User Stories

#### 6.1 As a Flutter Developer
- "I want to generate API client code from our OpenAPI spec so that I can integrate our backend APIs quickly"
- "I want to call APIs using intuitive syntax like `apiClient.users.getUser(id)` based on OpenAPI tags"
- "I want to subscribe to real-time updates using `apiClient.events.subscribeToUserEvents()` for SSE endpoints"
- "I want type-safe API calls so that I catch errors at compile time"
- "I want IDE auto-completion to discover available endpoints through the tag hierarchy"
- "I want the generated code to handle authentication automatically"
- "I want to generate code from remote URLs during CI/CD without storing spec files locally"

#### 6.2 As a Backend Developer
- "I want to generate client code for external APIs so that I can integrate third-party services efficiently"
- "I want the generated code to stay in sync with API changes"

#### 6.3 As a Team Lead
- "I want consistent API integration patterns across all projects"
- "I want to reduce onboarding time for new developers working with our APIs"

### 7. Non-Functional Requirements

#### 7.1 Reliability
- 99.9% uptime for code generation (no crashes)
- Graceful handling of malformed OpenAPI specifications
- Comprehensive error reporting and recovery

#### 7.2 Maintainability
- Modular, extensible architecture
- Comprehensive test coverage (>90%)
- Clear separation of concerns
- Well-documented codebase

#### 7.3 Security
- No sensitive data logging
- Secure handling of authentication tokens in generated code
- Input validation for OpenAPI specifications

### 8. Implementation Plan

#### Phase 1: Core Foundation (Months 1-2)
- OpenAPI 3.0 parser with tag extraction
- Multiple input source support (files, URLs)
- Basic model generation
- Hierarchical API client generation (`APIClient().tag.endpoint()` structure)
- Tag-based endpoint grouping
- Dio integration
- CLI tool framework with input source handling

#### Phase 2: Enhanced Features (Months 3-4)
- Comprehensive authentication handling (Basic, Bearer, API Key)
- Authentication interceptors and token management
- Per-endpoint authentication overrides
- Server-Sent Events (SSE) support and stream generation
- Token refresh mechanisms
- Advanced data types support
- Error handling patterns with auth-specific errors
- Documentation generation
- Build runner integration

#### Phase 3: Developer Experience (Months 5-6)
- Configuration system
- Watch mode
- IDE plugins
- Comprehensive documentation
- Example projects

#### Phase 4: Advanced Features (Months 7-8)
- Custom templates
- OpenAPI 2.0 support
- Performance optimizations
- Community feedback integration

### 9. Success Criteria

#### 9.1 Technical Success
- Successfully generates code for 95% of valid OpenAPI specifications
- Generated code compiles without errors
- Performance benchmarks met
- Zero critical security vulnerabilities

#### 9.2 Business Success
- 1,000+ downloads in first 3 months
- 4.5+ stars on pub.dev
- Active community engagement (GitHub issues, discussions)
- Adoption by 10+ notable Flutter/Dart projects

### 10. Risk Assessment

#### 10.1 Technical Risks
- **High:** OpenAPI specification complexity and edge cases
- **Medium:** Dio API changes affecting integration
- **Low:** Dart language evolution requiring updates

#### 10.2 Market Risks
- **Medium:** Competing solutions gaining market share
- **Low:** Reduced demand for REST API tooling

#### 10.3 Mitigation Strategies
- Comprehensive testing with diverse OpenAPI specifications
- Active monitoring of dependency updates
- Community engagement and feedback incorporation
- Flexible architecture for future adaptations

### 11. Dependencies & Constraints

#### 11.1 External Dependencies
- OpenAPI specification standard compliance
- Dio package stability and API compatibility
- Dart SDK evolution and breaking changes

#### 11.2 Resource Constraints
- Development team size and expertise
- Time-to-market pressure
- Community contribution availability

### 12. Future Considerations

#### 12.1 Potential Enhancements
- GraphQL support
- Multiple HTTP client backends
- Real-time API monitoring
- API mocking capabilities
- Multi-language code generation

#### 12.2 Ecosystem Integration
- Flutter DevTools integration
- Dart analyzer plugins
- CI/CD pipeline templates
- Cloud deployment integrations

### 13. API Client Structure Design

#### 13.1 Hierarchical Organization
The generated API client will follow a tag-based hierarchical structure that mirrors the OpenAPI specification organization:

```dart
// Usage Example:
final apiClient = APIClient(baseUrl: 'https://api.example.com');

// Configure authentication
apiClient.setBasicAuth(username: 'user', password: 'pass');
// OR
apiClient.setBearerToken('your-jwt-token');
// OR
apiClient.setApiKey(key: 'api-key', location: ApiKeyLocation.header);

// Access REST endpoints through tag groupings
final user = await apiClient.users.getUser(userId: '123');
final posts = await apiClient.posts.getUserPosts(userId: '123');
final result = await apiClient.auth.login(credentials: loginData);

// Access SSE endpoints for real-time data
final eventStream = apiClient.events.subscribeToUserEvents(userId: '123');
await for (final event in eventStream) {
  print('Received event: ${event.type} - ${event.data}');
}
```

#### 13.2 Input Source Support
The CLI tool supports multiple input sources for OpenAPI specifications:

```bash
# Local YAML file
dart_openapi_gen --input ./api-spec.yaml --output ./lib/api/

# Local JSON file
dart_openapi_gen --input ./api-spec.json --output ./lib/api/

# Remote URL
dart_openapi_gen --url https://api.example.com/openapi.json --output ./lib/api/

# Remote URL with authentication
dart_openapi_gen --url https://api.example.com/openapi.json --auth-header "Authorization: Bearer token" --output ./lib/api/

# Configuration file
dart_openapi_gen --config ./codegen.yaml
```

#### 13.3 Generated Structure
**Main API Client:**
- Root client class with configuration (base URL, headers, interceptors)
- Authentication management methods
- Lazy-initialized tag group properties
- Shared Dio instance with auth interceptors
- SSE client manager for real-time connections

**Tag Groups:**
- Each OpenAPI tag becomes a property on the main client
- Tag groups contain all endpoints associated with that tag
- Consistent naming conventions (camelCase transformation)
- Inherit authentication from parent client
- Support both REST and SSE endpoints

**Endpoint Methods:**
- Type-safe method signatures based on OpenAPI operation definitions
- Per-endpoint authentication override capabilities
- Parameter validation and serialization
- Response deserialization to generated model classes
- Comprehensive error handling with auth-specific errors
- SSE stream methods returning `Stream<T>` for real-time data

#### 13.4 Authentication Implementation
**Supported Authentication Types:**
```dart
// Basic Authentication
apiClient.setBasicAuth(username: 'user', password: 'password');

// Bearer Token (JWT/Token)
apiClient.setBearerToken('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');

// API Key Authentication
apiClient.setApiKey(
  key: 'your-api-key',
  location: ApiKeyLocation.header, // or query, cookie
  paramName: 'X-API-Key' // optional, defaults from OpenAPI spec
);

// Multiple auth schemes (if supported by API)
apiClient.setMultipleAuth([
  BasicAuth(username: 'user', password: 'pass'),
  ApiKeyAuth(key: 'key', location: ApiKeyLocation.header)
]);

// Per-endpoint auth override
await apiClient.users.getUser(
  userId: '123',
  auth: BearerAuth('different-token')
);
```

#### 13.5 Server-Sent Events (SSE) Support
**SSE Endpoint Generation:**
```dart
// Generated SSE methods return streams
Stream<UserEvent> subscribeToUserEvents({
  required String userId,
  Map<String, String>? headers,
  AuthScheme? auth,
}) {
  return _sseClient.subscribe<UserEvent>(
    '/users/$userId/events',
    headers: headers,
    auth: auth ?? _authManager.currentAuth,
    parser: (data) => UserEvent.fromJson(jsonDecode(data)),
  );
}

// Usage with error handling and reconnection
final eventStream = apiClient.events.subscribeToUserEvents(userId: '123');
eventStream.listen(
  (event) => print('Event: ${event.type}'),
  onError: (error) => print('SSE Error: $error'),
  onDone: () => print('SSE Connection closed'),
);
```

**SSE Features:**
- Automatic reconnection with exponential backoff
- Authentication header injection
- Event filtering and transformation
- Connection health monitoring
- Graceful error handling and recovery

#### 13.6 Code Generation Examples
```dart
// Generated main client with SSE support
class APIClient {
  final Dio _dio;
  final SSEClient _sseClient;
  final AuthManager _authManager;
  late final UsersTag users;
  late final PostsTag posts;
  late final EventsTag events;
  
  APIClient({required String baseUrl}) : 
    _dio = Dio(),
    _sseClient = SSEClient(baseUrl: baseUrl),
    _authManager = AuthManager() {
    _dio.options.baseUrl = baseUrl;
    _dio.interceptors.add(AuthInterceptor(_authManager));
    _sseClient.setAuthManager(_authManager);
    
    users = UsersTag(_dio, _sseClient, _authManager);
    posts = PostsTag(_dio, _sseClient, _authManager);
    events = EventsTag(_dio, _sseClient, _authManager);
  }
  
  void setBasicAuth({required String username, required String password}) {
    _authManager.setBasicAuth(username, password);
  }
  
  void setBearerToken(String token) {
    _authManager.setBearerToken(token);
  }
}

// Generated tag group with REST and SSE support
class EventsTag {
  final Dio _dio;
  final SSEClient _sseClient;
  final AuthManager _authManager;
  
  EventsTag(this._dio, this._sseClient, this._authManager);
  
  // REST endpoint
  Future<List<Event>> getEvents({int? limit}) async {
    // Implementation
  }
  
  // SSE endpoint
  Stream<UserEvent> subscribeToUserEvents({
    required String userId,
    AuthScheme? auth,
  }) {
    return _sseClient.subscribe<UserEvent>(
      '/events/users/$userId',
      auth: auth ?? _authManager.currentAuth,
      parser: (data) => UserEvent.fromJson(jsonDecode(data)),
      reconnectStrategy: ExponentialBackoff(),
    );
  }
}
```

#### 13.7 CLI Configuration
**Configuration File Example (codegen.yaml):**
```yaml
input:
  source: "https://api.example.com/openapi.json"
  # or: "./api-spec.yaml"
  auth_header: "Authorization: Bearer ${API_TOKEN}"
  timeout: 30s

output:
  directory: "./lib/generated/api"
  package_name: "my_api_client"

generation:
  naming_convention: "camelCase"
  null_safety: true
  generate_docs: true
  sse_support: true
  
authentication:
  default_schemes: ["bearer", "apiKey"]
  token_refresh: true
  
dio_options:
  connect_timeout: 5000
  receive_timeout: 10000
  send_timeout: 10000
```

---

**Document Version:** 1.0  
**Last Updated:** July 1, 2025  
**Next Review:** July 15, 2025