#!/usr/bin/env dart

/// Main entry point for the dart_openapi_gen CLI tool.
///
/// This executable provides command-line access to the OpenAPI code generator.
library;

import 'dart:io';
import 'package:openapi_generator/src/cli/cli_runner.dart';

/// Main function for the CLI application.
Future<void> main(List<String> arguments) async {
  final exitCode = await runCli(arguments);
  exit(exitCode);
}
