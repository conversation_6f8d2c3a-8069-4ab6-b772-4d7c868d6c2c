# Dart OpenAPI Generator

A comprehensive Dart package that automatically generates type-safe API client code from OpenAPI specifications, leveraging the Dio HTTP client for network operations.

## Features

✨ **Phase 1 Implementation (Current)**

- 🔍 **OpenAPI 3.0+ Support**: Parse specifications from local files (YAML/JSON) and remote URLs
- 🏗️ **Hierarchical API Client Structure**: Generate clients with intuitive `APIClient().tag.endpoint()` syntax
- 🎯 **Type-Safe Code Generation**: Null-safe Dart models with JSON serialization using `json_annotation`
- 🌐 **Dio Integration**: Built-in HTTP client with comprehensive error handling
- 🛠️ **CLI Tool**: Command-line interface for easy code generation
- 📦 **Tag-Based Organization**: Automatic endpoint grouping based on OpenAPI tags

🚀 **Coming in Future Phases**

- 🔐 Authentication support (Basic, Bearer, API Key)
- 📡 Server-Sent Events (SSE) streaming
- 🔄 Token refresh mechanisms
- ⚙️ Build runner integration
- 🎨 Custom templates and configuration

## Getting Started

### Prerequisites

- Dart SDK 3.0.0 or higher
- An OpenAPI 3.0+ specification (local file or remote URL)

### Installation

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  openapi_generator: ^0.1.0

dev_dependencies:
  build_runner: ^2.4.0
  json_serializable: ^6.7.0
```

### CLI Installation

Install the CLI tool globally:

```bash
dart pub global activate openapi_generator
```

## Usage

### Command Line Interface

Generate API client code from an OpenAPI specification:

```bash
# From local file
dart_openapi_gen --input ./api-spec.yaml --output ./lib/api

# From remote URL
dart_openapi_gen --url https://api.example.com/openapi.json --output ./lib/api

# With authentication
dart_openapi_gen --url https://api.example.com/openapi.json \
                 --auth-header "Authorization: Bearer your-token" \
                 --output ./lib/api

# Custom package name
dart_openapi_gen --input ./spec.yaml --output ./lib/api --package-name my_api
```

### Programmatic Usage

```dart
import 'package:openapi_generator/openapi_generator.dart';

// Parse OpenAPI specification
final spec = await OpenApiParser.parseFromUrl(
  'https://petstore3.swagger.io/api/v3/openapi.json',
);

// Generate API client code
final generator = CodeGenerator(
  spec: spec,
  outputDirectory: './lib/generated',
  packageName: 'petstore_api',
);

final result = await generator.generate();
if (result.success) {
  print('Generated ${result.generatedFiles.length} files');
}
```

### Using Generated Code

After generation, add the generated package to your dependencies and run code generation:

```bash
cd ./lib/generated
dart pub get
dart pub run build_runner build
```

Then use the generated API client:

```dart
import 'package:generated_api/generated_api.dart';

// Create API client
final client = ApiClient(baseUrl: 'https://api.example.com');

// Use hierarchical structure based on OpenAPI tags
final user = await client.users.getUser(userId: '123');
final posts = await client.posts.getUserPosts(userId: '123');
final result = await client.auth.login(requestBody: loginData);
```

## Generated Code Structure

The generator creates a hierarchical API client structure based on OpenAPI tags:

```
lib/generated/
├── generated_api.dart          # Main export file
├── api_client.dart            # Main API client class
├── models/                    # Generated model classes
│   ├── user.dart
│   ├── post.dart
│   └── ...
└── tags/                      # Tag-based endpoint groups
    ├── users_tag.dart
    ├── posts_tag.dart
    └── ...
```

### Example Generated Usage

```dart
// Main API client with tag-based organization
final apiClient = ApiClient(baseUrl: 'https://api.example.com');

// Access endpoints through tag groupings
final user = await apiClient.users.getUser(userId: '123');
final posts = await apiClient.posts.getUserPosts(userId: '123');
final newPost = await apiClient.posts.createPost(requestBody: postData);

// Type-safe models with JSON serialization
final userJson = user.data.toJson();
final userFromJson = User.fromJson(userJson);
```

## CLI Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--input` | `-i` | Path to OpenAPI spec file | - |
| `--url` | `-u` | URL to OpenAPI spec | - |
| `--output` | `-o` | Output directory | `./lib/generated` |
| `--package-name` | `-p` | Generated package name | `generated_api` |
| `--auth-header` | - | Auth header for URL requests | - |
| `--timeout` | - | URL request timeout (seconds) | `30` |
| `--clean` | - | Clean output directory first | `false` |
| `--verbose` | - | Enable verbose output | `false` |
| `--help` | `-h` | Show help message | - |
| `--version` | `-v` | Show version info | - |

## Examples

See the [example](example/) directory for complete usage examples, including:

- Parsing OpenAPI specifications from files and URLs
- Generating API client code
- Using the generated hierarchical client structure

## Contributing

Contributions are welcome! Please read our [contributing guidelines](CONTRIBUTING.md) and submit pull requests to our [GitHub repository](https://github.com/your-org/openapi_generator).

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Roadmap

This package follows the implementation plan outlined in our [Product Requirements Document](PRD.md):

- ✅ **Phase 1**: Core foundation with OpenAPI parsing and basic code generation
- 🔄 **Phase 2**: Authentication and SSE support (coming soon)
- 🔄 **Phase 3**: Developer experience improvements
- 🔄 **Phase 4**: Advanced features and community feedback integration

## Support

- 📖 [Documentation](https://pub.dev/packages/openapi_generator)
- 🐛 [Issue Tracker](https://github.com/your-org/openapi_generator/issues)
- 💬 [Discussions](https://github.com/your-org/openapi_generator/discussions)
