/// A comprehensive Dart package that automatically generates type-safe API client code from OpenAPI specifications.
///
/// This library provides tools for parsing OpenAPI specifications and generating
/// hierarchical API clients with Dio integration.
library openapi_generator;

// Core OpenAPI parsing
export 'src/openapi/openapi_parser.dart';
export 'src/openapi/openapi_spec_simple.dart';

// Code generation
export 'src/generator/api_client_generator.dart';
export 'src/generator/model_generator.dart';
export 'src/generator/code_generator.dart';

// CLI
export 'src/cli/cli_runner.dart';

// Generated client base classes
export 'src/runtime/api_client_base.dart';
export 'src/runtime/tag_base.dart';
