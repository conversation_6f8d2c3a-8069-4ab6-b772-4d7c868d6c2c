/// Base class for generated tag groups.
///
/// This file contains the base class for tag-based endpoint groupings
/// in the hierarchical API client structure.
library;

import 'package:dio/dio.dart';
import 'api_client_base.dart';

/// Base class for all generated tag groups.
///
/// Each OpenAPI tag becomes a class that extends this base,
/// providing organized access to related endpoints.
abstract class TagBase {
  /// The parent API client.
  final ApiClientBase apiClient;

  /// The Dio HTTP client instance.
  Dio get dio => apiClient.dio;

  /// The tag name this group represents.
  final String tagName;

  /// Creates a new tag group.
  const TagBase({required this.apiClient, required this.tagName});

  /// Executes a GET request.
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? responseParser,
  }) async {
    try {
      final response = await dio.get(
        path,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      final data = responseParser != null
          ? responseParser(response.data)
          : response.data as T;

      return ApiResponse.fromDioResponse(response, data);
    } catch (e) {
      rethrow;
    }
  }

  /// Executes a POST request.
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? responseParser,
  }) async {
    try {
      final response = await dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      final responseData = responseParser != null
          ? responseParser(response.data)
          : response.data as T;

      return ApiResponse.fromDioResponse(response, responseData);
    } catch (e) {
      rethrow;
    }
  }

  /// Executes a PUT request.
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? responseParser,
  }) async {
    try {
      final response = await dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      final responseData = responseParser != null
          ? responseParser(response.data)
          : response.data as T;

      return ApiResponse.fromDioResponse(response, responseData);
    } catch (e) {
      rethrow;
    }
  }

  /// Executes a DELETE request.
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? responseParser,
  }) async {
    try {
      final response = await dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      final responseData = responseParser != null
          ? responseParser(response.data)
          : response.data as T;

      return ApiResponse.fromDioResponse(response, responseData);
    } catch (e) {
      rethrow;
    }
  }

  /// Executes a PATCH request.
  Future<ApiResponse<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? responseParser,
  }) async {
    try {
      final response = await dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      final responseData = responseParser != null
          ? responseParser(response.data)
          : response.data as T;

      return ApiResponse.fromDioResponse(response, responseData);
    } catch (e) {
      rethrow;
    }
  }

  /// Executes a HEAD request.
  Future<ApiResponse<void>> head(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await dio.head(
        path,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      return ApiResponse.fromDioResponse(response, null);
    } catch (e) {
      rethrow;
    }
  }

  /// Executes an OPTIONS request.
  Future<ApiResponse<T>> options<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? responseParser,
  }) async {
    try {
      final response = await dio.request(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(method: 'OPTIONS', headers: headers),
      );

      final responseData = responseParser != null
          ? responseParser(response.data)
          : response.data as T;

      return ApiResponse.fromDioResponse(response, responseData);
    } catch (e) {
      rethrow;
    }
  }

  /// Builds query parameters from a map, filtering out null values.
  Map<String, dynamic> buildQueryParameters(Map<String, dynamic?> parameters) {
    final result = <String, dynamic>{};
    parameters.forEach((key, value) {
      if (value != null) {
        result[key] = value;
      }
    });
    return result;
  }

  /// Builds headers from a map, filtering out null values.
  Map<String, String> buildHeaders(Map<String, String?> headers) {
    final result = <String, String>{};
    headers.forEach((key, value) {
      if (value != null) {
        result[key] = value;
      }
    });
    return result;
  }

  /// Validates required parameters.
  void validateRequired(Map<String, dynamic?> parameters) {
    final missing = <String>[];
    parameters.forEach((key, value) {
      if (value == null) {
        missing.add(key);
      }
    });

    if (missing.isNotEmpty) {
      throw ArgumentError('Missing required parameters: ${missing.join(', ')}');
    }
  }

  /// Builds a path with path parameters.
  String buildPath(String pathTemplate, Map<String, dynamic> pathParameters) {
    var path = pathTemplate;
    pathParameters.forEach((key, value) {
      path = path.replaceAll('{$key}', value.toString());
    });
    return path;
  }
}

/// Utility class for parameter validation and conversion.
class ParameterUtils {
  /// Converts a value to a string for use in query parameters or headers.
  static String? convertToString(dynamic value) {
    if (value == null) return null;
    if (value is String) return value;
    if (value is bool) return value.toString();
    if (value is num) return value.toString();
    if (value is DateTime) return value.toIso8601String();
    if (value is List) return value.map((e) => e.toString()).join(',');
    return value.toString();
  }

  /// Validates that a parameter is not null if it's required.
  static void validateRequired(String name, dynamic value) {
    if (value == null) {
      throw ArgumentError('Required parameter "$name" cannot be null');
    }
  }

  /// Validates that a string parameter is not empty if it's required.
  static void validateNotEmpty(String name, String? value) {
    if (value == null || value.isEmpty) {
      throw ArgumentError('Required parameter "$name" cannot be null or empty');
    }
  }

  /// Validates that a list parameter is not empty if it's required.
  static void validateListNotEmpty(String name, List? value) {
    if (value == null || value.isEmpty) {
      throw ArgumentError('Required parameter "$name" cannot be null or empty');
    }
  }
}
