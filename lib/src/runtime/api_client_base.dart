/// Base class for generated API clients.
///
/// This file contains the base classes and utilities for the generated API client code.
library;

import 'package:dio/dio.dart';

/// Base class for all generated API clients.
///
/// Provides common functionality for HTTP operations, configuration,
/// and error handling using Dio.
abstract class ApiClientBase {
  /// The Dio HTTP client instance.
  final Dio dio;

  /// The base URL for API requests.
  final String baseUrl;

  /// Default headers to include with all requests.
  final Map<String, String> defaultHeaders;

  /// Creates a new API client base.
  ApiClientBase({
    required this.baseUrl,
    this.defaultHeaders = const {},
    Dio? dioInstance,
  }) : dio = dioInstance ?? Dio() {
    // Configure Dio with base settings
    dio.options.baseUrl = baseUrl;
    dio.options.headers.addAll(defaultHeaders);
    
    // Add default interceptors
    dio.interceptors.add(_createLoggingInterceptor());
    dio.interceptors.add(_createErrorInterceptor());
  }

  /// Configures request timeout settings.
  void configureTimeout({
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
  }) {
    if (connectTimeout != null) {
      dio.options.connectTimeout = connectTimeout;
    }
    if (receiveTimeout != null) {
      dio.options.receiveTimeout = receiveTimeout;
    }
    if (sendTimeout != null) {
      dio.options.sendTimeout = sendTimeout;
    }
  }

  /// Adds a custom header to all requests.
  void setHeader(String name, String value) {
    dio.options.headers[name] = value;
  }

  /// Removes a header from all requests.
  void removeHeader(String name) {
    dio.options.headers.remove(name);
  }

  /// Adds a custom interceptor to the Dio instance.
  void addInterceptor(Interceptor interceptor) {
    dio.interceptors.add(interceptor);
  }

  /// Creates a logging interceptor for debugging.
  Interceptor _createLoggingInterceptor() {
    return LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: true,
      error: true,
      logPrint: (object) {
        // Only log in debug mode
        if (const bool.fromEnvironment('dart.vm.product') == false) {
          print(object);
        }
      },
    );
  }

  /// Creates an error handling interceptor.
  Interceptor _createErrorInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) {
        // Convert DioException to ApiException for better error handling
        if (error is DioException) {
          final apiException = _convertDioException(error);
          handler.reject(DioException(
            requestOptions: error.requestOptions,
            error: apiException,
            type: error.type,
            response: error.response,
          ));
        } else {
          handler.next(error);
        }
      },
    );
  }

  /// Converts a DioException to an ApiException.
  ApiException _convertDioException(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException(
          message: 'Request timeout',
          type: ApiExceptionType.timeout,
          statusCode: null,
          originalError: error,
        );
      case DioExceptionType.badResponse:
        return ApiException(
          message: error.response?.data?.toString() ?? 'Bad response',
          type: ApiExceptionType.serverError,
          statusCode: error.response?.statusCode,
          originalError: error,
        );
      case DioExceptionType.cancel:
        return ApiException(
          message: 'Request cancelled',
          type: ApiExceptionType.cancelled,
          statusCode: null,
          originalError: error,
        );
      case DioExceptionType.connectionError:
        return ApiException(
          message: 'Connection error',
          type: ApiExceptionType.networkError,
          statusCode: null,
          originalError: error,
        );
      case DioExceptionType.badCertificate:
        return ApiException(
          message: 'Bad certificate',
          type: ApiExceptionType.networkError,
          statusCode: null,
          originalError: error,
        );
      case DioExceptionType.unknown:
      default:
        return ApiException(
          message: error.message ?? 'Unknown error',
          type: ApiExceptionType.unknown,
          statusCode: null,
          originalError: error,
        );
    }
  }
}

/// Custom exception for API errors.
class ApiException implements Exception {
  /// The error message.
  final String message;

  /// The type of API exception.
  final ApiExceptionType type;

  /// The HTTP status code, if available.
  final int? statusCode;

  /// The original error that caused this exception.
  final dynamic originalError;

  const ApiException({
    required this.message,
    required this.type,
    this.statusCode,
    this.originalError,
  });

  @override
  String toString() {
    final buffer = StringBuffer('ApiException: $message');
    if (statusCode != null) {
      buffer.write(' (Status: $statusCode)');
    }
    if (originalError != null) {
      buffer.write(' [Original: $originalError]');
    }
    return buffer.toString();
  }

  /// Returns true if this is a client error (4xx status code).
  bool get isClientError => statusCode != null && statusCode! >= 400 && statusCode! < 500;

  /// Returns true if this is a server error (5xx status code).
  bool get isServerError => statusCode != null && statusCode! >= 500;

  /// Returns true if this is a network-related error.
  bool get isNetworkError => type == ApiExceptionType.networkError || type == ApiExceptionType.timeout;
}

/// Types of API exceptions.
enum ApiExceptionType {
  /// Network connectivity error.
  networkError,

  /// Request timeout.
  timeout,

  /// Server returned an error response.
  serverError,

  /// Request was cancelled.
  cancelled,

  /// Unknown error.
  unknown,
}

/// Utility class for handling API responses.
class ApiResponse<T> {
  /// The response data.
  final T data;

  /// The HTTP status code.
  final int statusCode;

  /// The response headers.
  final Map<String, dynamic> headers;

  /// The original Dio response.
  final Response<dynamic> originalResponse;

  const ApiResponse({
    required this.data,
    required this.statusCode,
    required this.headers,
    required this.originalResponse,
  });

  /// Creates an ApiResponse from a Dio Response.
  factory ApiResponse.fromDioResponse(Response<dynamic> response, T data) {
    return ApiResponse(
      data: data,
      statusCode: response.statusCode ?? 0,
      headers: response.headers.map,
      originalResponse: response,
    );
  }

  /// Returns true if the response indicates success (2xx status code).
  bool get isSuccess => statusCode >= 200 && statusCode < 300;
}

/// Configuration options for API clients.
class ApiClientConfig {
  /// The base URL for the API.
  final String baseUrl;

  /// Default headers to include with all requests.
  final Map<String, String> defaultHeaders;

  /// Connection timeout duration.
  final Duration? connectTimeout;

  /// Receive timeout duration.
  final Duration? receiveTimeout;

  /// Send timeout duration.
  final Duration? sendTimeout;

  /// Whether to enable request/response logging.
  final bool enableLogging;

  const ApiClientConfig({
    required this.baseUrl,
    this.defaultHeaders = const {},
    this.connectTimeout,
    this.receiveTimeout,
    this.sendTimeout,
    this.enableLogging = false,
  });

  /// Creates a copy of this config with updated values.
  ApiClientConfig copyWith({
    String? baseUrl,
    Map<String, String>? defaultHeaders,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
    bool? enableLogging,
  }) {
    return ApiClientConfig(
      baseUrl: baseUrl ?? this.baseUrl,
      defaultHeaders: defaultHeaders ?? this.defaultHeaders,
      connectTimeout: connectTimeout ?? this.connectTimeout,
      receiveTimeout: receiveTimeout ?? this.receiveTimeout,
      sendTimeout: sendTimeout ?? this.sendTimeout,
      enableLogging: enableLogging ?? this.enableLogging,
    );
  }
}
