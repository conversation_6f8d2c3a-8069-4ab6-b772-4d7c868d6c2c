/// Command-line interface for the OpenAPI generator.
///
/// This file contains the CLI runner that handles command-line arguments
/// and orchestrates the code generation process.
library;

import 'dart:io';
import 'package:args/args.dart';
import 'package:path/path.dart' as path;

import '../openapi/openapi_parser.dart';
import '../generator/code_generator.dart';

/// CLI runner for the OpenAPI generator.
class CliRunner {
  /// The argument parser.
  late final ArgParser _parser;

  /// Creates a new CLI runner.
  CliRunner() {
    _parser = _createArgParser();
  }

  /// Runs the CLI with the given arguments.
  Future<int> run(List<String> arguments) async {
    try {
      final results = _parser.parse(arguments);

      // Handle help
      if (results['help'] as bool) {
        _printUsage();
        return 0;
      }

      // Handle version
      if (results['version'] as bool) {
        _printVersion();
        return 0;
      }

      // Validate arguments
      final validationResult = _validateArguments(results);
      if (validationResult != null) {
        stderr.writeln('Error: $validationResult');
        stderr.writeln();
        _printUsage();
        return 1;
      }

      // Parse input source
      final inputSource = _parseInputSource(results);
      if (inputSource == null) {
        stderr.writeln('Error: No input source specified. Use --input or --url.');
        return 1;
      }

      // Get output directory
      final outputDir = results['output'] as String;

      // Parse OpenAPI specification
      stdout.writeln('Parsing OpenAPI specification...');
      final spec = await inputSource.parse();
      stdout.writeln('Successfully parsed OpenAPI ${spec.openapi} specification: ${spec.info.title}');

      // Generate code
      stdout.writeln('Generating code...');
      final generator = CodeGenerator(
        spec: spec,
        outputDirectory: outputDir,
        packageName: results['package-name'] as String? ?? 'generated_api',
      );

      final result = await generator.generate();

      if (result.success) {
        stdout.writeln('✅ ${result.message}');
        stdout.writeln('Generated files:');
        for (final file in result.generatedFiles) {
          stdout.writeln('  📄 $file');
        }
        
        // Print next steps
        _printNextSteps(outputDir);
        
        return 0;
      } else {
        stderr.writeln('❌ ${result.message}');
        if (result.error != null) {
          stderr.writeln('Error details: ${result.error}');
        }
        return 1;
      }

    } catch (e, stackTrace) {
      stderr.writeln('❌ Unexpected error: $e');
      if (Platform.environment['DART_OPENAPI_DEBUG'] == 'true') {
        stderr.writeln('Stack trace:');
        stderr.writeln(stackTrace);
      }
      return 1;
    }
  }

  /// Creates the argument parser.
  ArgParser _createArgParser() {
    return ArgParser()
      ..addFlag(
        'help',
        abbr: 'h',
        help: 'Show this help message.',
        negatable: false,
      )
      ..addFlag(
        'version',
        abbr: 'v',
        help: 'Show version information.',
        negatable: false,
      )
      ..addOption(
        'input',
        abbr: 'i',
        help: 'Path to the OpenAPI specification file (YAML or JSON).',
      )
      ..addOption(
        'url',
        abbr: 'u',
        help: 'URL to the OpenAPI specification (YAML or JSON).',
      )
      ..addOption(
        'output',
        abbr: 'o',
        help: 'Output directory for generated code.',
        defaultsTo: './lib/generated',
      )
      ..addOption(
        'package-name',
        abbr: 'p',
        help: 'Name for the generated package.',
        defaultsTo: 'generated_api',
      )
      ..addOption(
        'auth-header',
        help: 'Authentication header for URL requests (e.g., "Authorization: Bearer token").',
      )
      ..addOption(
        'timeout',
        help: 'Timeout for URL requests in seconds.',
        defaultsTo: '30',
      )
      ..addFlag(
        'clean',
        help: 'Clean the output directory before generation.',
        defaultsTo: false,
      )
      ..addFlag(
        'verbose',
        help: 'Enable verbose output.',
        defaultsTo: false,
      );
  }

  /// Validates the command-line arguments.
  String? _validateArguments(ArgResults results) {
    // Check that either input or url is provided
    final hasInput = results['input'] != null;
    final hasUrl = results['url'] != null;

    if (!hasInput && !hasUrl) {
      return 'Either --input or --url must be specified.';
    }

    if (hasInput && hasUrl) {
      return 'Cannot specify both --input and --url. Choose one.';
    }

    // Validate package name
    final packageName = results['package-name'] as String?;
    if (packageName != null && !_isValidPackageName(packageName)) {
      return 'Invalid package name. Package names must be lowercase with underscores only.';
    }

    // Validate timeout
    final timeoutStr = results['timeout'] as String;
    final timeout = int.tryParse(timeoutStr);
    if (timeout == null || timeout <= 0) {
      return 'Timeout must be a positive integer.';
    }

    return null;
  }

  /// Parses the input source from arguments.
  OpenApiInputSource? _parseInputSource(ArgResults results) {
    final inputFile = results['input'] as String?;
    final inputUrl = results['url'] as String?;
    final authHeader = results['auth-header'] as String?;
    final timeoutStr = results['timeout'] as String;
    final timeout = Duration(seconds: int.parse(timeoutStr));

    if (inputFile != null) {
      return OpenApiInputSource.file(inputFile);
    }

    if (inputUrl != null) {
      final headers = <String, String>{};
      if (authHeader != null) {
        final parts = authHeader.split(':');
        if (parts.length >= 2) {
          final key = parts[0].trim();
          final value = parts.sublist(1).join(':').trim();
          headers[key] = value;
        }
      }

      return OpenApiInputSource.url(
        inputUrl,
        headers: headers.isNotEmpty ? headers : null,
        timeout: timeout,
      );
    }

    return null;
  }

  /// Prints the usage information.
  void _printUsage() {
    stdout.writeln('Dart OpenAPI Generator');
    stdout.writeln('Generates type-safe API client code from OpenAPI specifications.');
    stdout.writeln();
    stdout.writeln('Usage: dart_openapi_gen [options]');
    stdout.writeln();
    stdout.writeln('Options:');
    stdout.writeln(_parser.usage);
    stdout.writeln();
    stdout.writeln('Examples:');
    stdout.writeln('  # Generate from local file');
    stdout.writeln('  dart_openapi_gen --input ./api-spec.yaml --output ./lib/api');
    stdout.writeln();
    stdout.writeln('  # Generate from remote URL');
    stdout.writeln('  dart_openapi_gen --url https://api.example.com/openapi.json --output ./lib/api');
    stdout.writeln();
    stdout.writeln('  # Generate with authentication');
    stdout.writeln('  dart_openapi_gen --url https://api.example.com/openapi.json \\');
    stdout.writeln('                   --auth-header "Authorization: Bearer your-token" \\');
    stdout.writeln('                   --output ./lib/api');
  }

  /// Prints version information.
  void _printVersion() {
    stdout.writeln('Dart OpenAPI Generator v0.1.0');
    stdout.writeln('A comprehensive tool for generating type-safe API clients from OpenAPI specifications.');
  }

  /// Prints next steps after successful generation.
  void _printNextSteps(String outputDir) {
    stdout.writeln();
    stdout.writeln('🎉 Code generation completed successfully!');
    stdout.writeln();
    stdout.writeln('Next steps:');
    stdout.writeln('1. Add the generated package to your pubspec.yaml dependencies:');
    stdout.writeln('   dependencies:');
    stdout.writeln('     generated_api:');
    stdout.writeln('       path: ${path.relative(outputDir)}');
    stdout.writeln();
    stdout.writeln('2. Run code generation for JSON serialization:');
    stdout.writeln('   cd ${path.relative(outputDir)}');
    stdout.writeln('   dart pub get');
    stdout.writeln('   dart pub run build_runner build');
    stdout.writeln();
    stdout.writeln('3. Import and use the generated API client:');
    stdout.writeln('   import \'package:generated_api/generated_api.dart\';');
    stdout.writeln();
    stdout.writeln('   final client = ApiClient(baseUrl: \'https://api.example.com\');');
    stdout.writeln('   final result = await client.users.getUser(userId: \'123\');');
  }

  /// Validates that a package name is valid.
  bool _isValidPackageName(String packageName) {
    final regex = RegExp(r'^[a-z][a-z0-9_]*$');
    return regex.hasMatch(packageName);
  }
}

/// Entry point for the CLI application.
Future<int> runCli(List<String> arguments) async {
  final runner = CliRunner();
  return await runner.run(arguments);
}
