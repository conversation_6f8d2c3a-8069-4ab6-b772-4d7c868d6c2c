/// OpenAPI specification data models.
///
/// This file contains simplified core data structures for representing OpenAPI 3.0 specifications.
/// This is a Phase 1 implementation focusing on basic functionality.
library;

/// Represents an OpenAPI 3.0 specification.
class OpenApiSpec {
  /// The OpenAPI version (e.g., "3.0.0").
  final String openapi;

  /// Metadata about the API.
  final Info info;

  /// The available paths and operations for the API.
  final Map<String, PathItem> paths;

  /// A list of tags used by the specification with additional metadata.
  final List<Tag>? tags;

  const OpenApiSpec({
    required this.openapi,
    required this.info,
    required this.paths,
    this.tags,
  });

  factory OpenApiSpec.fromJson(Map<String, dynamic> json) {
    return OpenApiSpec(
      openapi: json['openapi'] as String,
      info: Info.fromJson(json['info'] as Map<String, dynamic>),
      paths: (json['paths'] as Map<String, dynamic>).map(
        (key, value) =>
            MapEntry(key, PathItem.fromJson(value as Map<String, dynamic>)),
      ),
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => Tag.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'openapi': openapi,
      'info': info.toJson(),
      'paths': paths.map((key, value) => MapEntry(key, value.toJson())),
      if (tags != null) 'tags': tags!.map((e) => e.toJson()).toList(),
    };
  }

  /// Gets all unique tags from the specification.
  List<String> getAllTags() {
    final tagSet = <String>{};

    // Add tags from the global tags list
    if (tags != null) {
      tagSet.addAll(tags!.map((tag) => tag.name));
    }

    // Add tags from operations
    for (final pathItem in paths.values) {
      for (final operation in pathItem.getAllOperations()) {
        if (operation.tags != null) {
          tagSet.addAll(operation.tags!);
        }
      }
    }

    return tagSet.toList()..sort();
  }
}

/// Metadata about the API.
class Info {
  /// The title of the API.
  final String title;

  /// A short description of the API.
  final String? description;

  /// The version of the OpenAPI document.
  final String version;

  const Info({required this.title, this.description, required this.version});

  factory Info.fromJson(Map<String, dynamic> json) {
    return Info(
      title: json['title'] as String,
      description: json['description'] as String?,
      version: json['version'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      if (description != null) 'description': description,
      'version': version,
    };
  }
}

/// Server connectivity information.
class Server {
  /// A URL to the target host.
  final String url;

  /// An optional string describing the host designated by the URL.
  final String? description;

  const Server({required this.url, this.description});

  factory Server.fromJson(Map<String, dynamic> json) {
    return Server(
      url: json['url'] as String,
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {'url': url, if (description != null) 'description': description};
  }
}

/// Describes the operations available on a single path.
@JsonSerializable()
class PathItem {
  /// A definition of a GET operation on this path.
  final Operation? get;

  /// A definition of a PUT operation on this path.
  final Operation? put;

  /// A definition of a POST operation on this path.
  final Operation? post;

  /// A definition of a DELETE operation on this path.
  final Operation? delete;

  /// A definition of a OPTIONS operation on this path.
  final Operation? options;

  /// A definition of a HEAD operation on this path.
  final Operation? head;

  /// A definition of a PATCH operation on this path.
  final Operation? patch;

  /// A definition of a TRACE operation on this path.
  final Operation? trace;

  const PathItem({
    this.get,
    this.put,
    this.post,
    this.delete,
    this.options,
    this.head,
    this.patch,
    this.trace,
  });

  factory PathItem.fromJson(Map<String, dynamic> json) =>
      _$PathItemFromJson(json);
  Map<String, dynamic> toJson() => _$PathItemToJson(this);

  /// Gets all operations defined in this path item.
  List<Operation> getAllOperations() {
    return [
      if (get != null) get!,
      if (put != null) put!,
      if (post != null) post!,
      if (delete != null) delete!,
      if (options != null) options!,
      if (head != null) head!,
      if (patch != null) patch!,
      if (trace != null) trace!,
    ];
  }
}

/// Describes a single API operation on a path.
@JsonSerializable()
class Operation {
  /// A list of tags for API documentation control.
  final List<String>? tags;

  /// A short summary of what the operation does.
  final String? summary;

  /// A verbose explanation of the operation behavior.
  final String? description;

  /// Unique string used to identify the operation.
  final String? operationId;

  /// A list of parameters that are applicable for this operation.
  final List<Parameter>? parameters;

  /// The request body applicable for this operation.
  final RequestBody? requestBody;

  /// The list of possible responses as they are returned from executing this operation.
  final Map<String, Response> responses;

  const Operation({
    this.tags,
    this.summary,
    this.description,
    this.operationId,
    this.parameters,
    this.requestBody,
    required this.responses,
  });

  factory Operation.fromJson(Map<String, dynamic> json) =>
      _$OperationFromJson(json);
  Map<String, dynamic> toJson() => _$OperationToJson(this);
}

/// Describes a single operation parameter.
@JsonSerializable()
class Parameter {
  /// The name of the parameter.
  final String name;

  /// The location of the parameter.
  @JsonKey(name: 'in')
  final String location;

  /// A brief description of the parameter.
  final String? description;

  /// Determines whether this parameter is mandatory.
  final bool? required;

  /// The schema defining the type used for the parameter.
  final Schema? schema;

  const Parameter({
    required this.name,
    required this.location,
    this.description,
    this.required,
    this.schema,
  });

  factory Parameter.fromJson(Map<String, dynamic> json) =>
      _$ParameterFromJson(json);
  Map<String, dynamic> toJson() => _$ParameterToJson(this);
}

/// Describes a request body.
@JsonSerializable()
class RequestBody {
  /// A brief description of the request body.
  final String? description;

  /// The content of the request body.
  final Map<String, MediaType> content;

  /// Determines if the request body is required in the request.
  final bool? required;

  const RequestBody({this.description, required this.content, this.required});

  factory RequestBody.fromJson(Map<String, dynamic> json) =>
      _$RequestBodyFromJson(json);
  Map<String, dynamic> toJson() => _$RequestBodyToJson(this);
}

/// Describes a single response from an API Operation.
@JsonSerializable()
class Response {
  /// A short description of the response.
  final String description;

  /// A map containing descriptions of potential response payloads.
  final Map<String, MediaType>? content;

  const Response({required this.description, this.content});

  factory Response.fromJson(Map<String, dynamic> json) =>
      _$ResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ResponseToJson(this);
}

/// Each Media Type Object provides schema and examples for the media type identified by its key.
@JsonSerializable()
class MediaType {
  /// The schema defining the content of the request, response, or parameter.
  final Schema? schema;

  const MediaType({this.schema});

  factory MediaType.fromJson(Map<String, dynamic> json) =>
      _$MediaTypeFromJson(json);
  Map<String, dynamic> toJson() => _$MediaTypeToJson(this);
}

/// The Schema Object allows the definition of input and output data types.
@JsonSerializable()
class Schema {
  /// The type of the schema.
  final String? type;

  /// The format of the schema.
  final String? format;

  /// The items schema for array types.
  final Schema? items;

  /// The properties for object types.
  final Map<String, Schema>? properties;

  /// List of required properties.
  final List<String>? required;

  /// Reference to another schema.
  @JsonKey(name: '\$ref')
  final String? ref;

  const Schema({
    this.type,
    this.format,
    this.items,
    this.properties,
    this.required,
    this.ref,
  });

  factory Schema.fromJson(Map<String, dynamic> json) => _$SchemaFromJson(json);
  Map<String, dynamic> toJson() => _$SchemaToJson(this);
}

/// Holds a set of reusable objects for different aspects of the OAS.
@JsonSerializable()
class Components {
  /// An object to hold reusable Schema Objects.
  final Map<String, Schema>? schemas;

  const Components({this.schemas});

  factory Components.fromJson(Map<String, dynamic> json) =>
      _$ComponentsFromJson(json);
  Map<String, dynamic> toJson() => _$ComponentsToJson(this);
}

/// Adds metadata to a single tag that is used by the Operation Object.
@JsonSerializable()
class Tag {
  /// The name of the tag.
  final String name;

  /// A short description for the tag.
  final String? description;

  const Tag({required this.name, this.description});

  factory Tag.fromJson(Map<String, dynamic> json) => _$TagFromJson(json);
  Map<String, dynamic> toJson() => _$TagToJson(this);
}
