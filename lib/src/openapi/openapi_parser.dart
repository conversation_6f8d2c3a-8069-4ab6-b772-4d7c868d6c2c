/// OpenAPI specification parser.
///
/// This file contains the parser for reading OpenAPI specifications from various sources.
library;

import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:yaml/yaml.dart';

import 'openapi_spec_simple.dart';

/// Exception thrown when parsing OpenAPI specifications fails.
class OpenApiParseException implements Exception {
  final String message;
  final dynamic cause;

  const OpenApiParseException(this.message, [this.cause]);

  @override
  String toString() =>
      'OpenApiParseException: $message${cause != null ? ' (caused by: $cause)' : ''}';
}

/// Parser for OpenAPI 3.0 specifications.
class OpenApiParser {
  /// Parses an OpenAPI specification from a local file.
  ///
  /// Supports both YAML and JSON formats.
  /// The file format is determined by the file extension.
  static Future<OpenApiSpec> parseFromFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw OpenApiParseException('File not found: $filePath');
      }

      final content = await file.readAsString();
      final extension = filePath.toLowerCase().split('.').last;

      switch (extension) {
        case 'yaml':
        case 'yml':
          return _parseYaml(content);
        case 'json':
          return _parseJson(content);
        default:
          throw OpenApiParseException(
            'Unsupported file format: $extension. Supported formats: yaml, yml, json',
          );
      }
    } catch (e) {
      if (e is OpenApiParseException) rethrow;
      throw OpenApiParseException('Failed to parse file: $filePath', e);
    }
  }

  /// Parses an OpenAPI specification from a remote URL.
  ///
  /// Supports both YAML and JSON formats.
  /// The format is determined by the Content-Type header or URL extension.
  static Future<OpenApiSpec> parseFromUrl(
    String url, {
    Map<String, String>? headers,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    try {
      final uri = Uri.parse(url);
      final client = http.Client();

      try {
        final response = await client
            .get(uri, headers: headers)
            .timeout(timeout);

        if (response.statusCode != 200) {
          throw OpenApiParseException(
            'Failed to fetch OpenAPI spec from $url. Status: ${response.statusCode}',
          );
        }

        final contentType =
            response.headers['content-type']?.toLowerCase() ?? '';
        final content = response.body;

        // Determine format from Content-Type header or URL extension
        if (contentType.contains('yaml') ||
            contentType.contains('yml') ||
            url.toLowerCase().endsWith('.yaml') ||
            url.toLowerCase().endsWith('.yml')) {
          return _parseYaml(content);
        } else if (contentType.contains('json') ||
            url.toLowerCase().endsWith('.json')) {
          return _parseJson(content);
        } else {
          // Try to parse as JSON first, then YAML
          try {
            return _parseJson(content);
          } catch (_) {
            return _parseYaml(content);
          }
        }
      } finally {
        client.close();
      }
    } catch (e) {
      if (e is OpenApiParseException) rethrow;
      throw OpenApiParseException('Failed to parse URL: $url', e);
    }
  }

  /// Parses an OpenAPI specification from a JSON string.
  static OpenApiSpec parseFromJsonString(String jsonString) {
    try {
      return _parseJson(jsonString);
    } catch (e) {
      throw OpenApiParseException('Failed to parse JSON string', e);
    }
  }

  /// Parses an OpenAPI specification from a YAML string.
  static OpenApiSpec parseFromYamlString(String yamlString) {
    try {
      return _parseYaml(yamlString);
    } catch (e) {
      throw OpenApiParseException('Failed to parse YAML string', e);
    }
  }

  /// Validates that the OpenAPI specification is supported.
  static void validateSpec(OpenApiSpec spec) {
    // Check OpenAPI version
    final version = spec.openapi;
    if (!version.startsWith('3.0')) {
      throw OpenApiParseException(
        'Unsupported OpenAPI version: $version. Only OpenAPI 3.0.x is supported.',
      );
    }

    // Validate required fields
    if (spec.info.title.isEmpty) {
      throw OpenApiParseException('OpenAPI spec must have a non-empty title');
    }

    if (spec.info.version.isEmpty) {
      throw OpenApiParseException('OpenAPI spec must have a non-empty version');
    }

    if (spec.paths.isEmpty) {
      throw OpenApiParseException('OpenAPI spec must have at least one path');
    }

    // Validate that all operations have at least one tag for hierarchical structure
    final operationsWithoutTags = <String>[];
    spec.paths.forEach((path, pathItem) {
      pathItem.getAllOperations().forEach((operation) {
        if (operation.tags == null || operation.tags!.isEmpty) {
          operationsWithoutTags.add(
            '${operation.operationId ?? 'unnamed'} at $path',
          );
        }
      });
    });

    if (operationsWithoutTags.isNotEmpty) {
      throw OpenApiParseException(
        'All operations must have at least one tag for hierarchical client generation. '
        'Operations without tags: ${operationsWithoutTags.join(', ')}',
      );
    }
  }

  /// Parses JSON content into an OpenAPI specification.
  static OpenApiSpec _parseJson(String content) {
    try {
      final json = jsonDecode(content) as Map<String, dynamic>;
      final spec = OpenApiSpec.fromJson(json);
      validateSpec(spec);
      return spec;
    } catch (e) {
      if (e is OpenApiParseException) rethrow;
      throw OpenApiParseException('Invalid JSON format', e);
    }
  }

  /// Parses YAML content into an OpenAPI specification.
  static OpenApiSpec _parseYaml(String content) {
    try {
      final yaml = loadYaml(content);
      final json = _yamlToJson(yaml);
      final spec = OpenApiSpec.fromJson(json);
      validateSpec(spec);
      return spec;
    } catch (e) {
      if (e is OpenApiParseException) rethrow;
      throw OpenApiParseException('Invalid YAML format', e);
    }
  }

  /// Converts YAML data to JSON-compatible Map.
  static dynamic _yamlToJson(dynamic yaml) {
    if (yaml is YamlMap) {
      final map = <String, dynamic>{};
      yaml.forEach((key, value) {
        map[key.toString()] = _yamlToJson(value);
      });
      return map;
    } else if (yaml is YamlList) {
      return yaml.map(_yamlToJson).toList();
    } else {
      return yaml;
    }
  }
}

/// Input source configuration for OpenAPI specifications.
class OpenApiInputSource {
  /// The type of input source.
  final OpenApiInputType type;

  /// The source path, URL, or content.
  final String source;

  /// Additional headers for URL sources.
  final Map<String, String>? headers;

  /// Timeout for URL sources.
  final Duration timeout;

  const OpenApiInputSource({
    required this.type,
    required this.source,
    this.headers,
    this.timeout = const Duration(seconds: 30),
  });

  /// Creates an input source for a local file.
  factory OpenApiInputSource.file(String filePath) {
    return OpenApiInputSource(type: OpenApiInputType.file, source: filePath);
  }

  /// Creates an input source for a remote URL.
  factory OpenApiInputSource.url(
    String url, {
    Map<String, String>? headers,
    Duration timeout = const Duration(seconds: 30),
  }) {
    return OpenApiInputSource(
      type: OpenApiInputType.url,
      source: url,
      headers: headers,
      timeout: timeout,
    );
  }

  /// Creates an input source for JSON content.
  factory OpenApiInputSource.jsonString(String content) {
    return OpenApiInputSource(
      type: OpenApiInputType.jsonString,
      source: content,
    );
  }

  /// Creates an input source for YAML content.
  factory OpenApiInputSource.yamlString(String content) {
    return OpenApiInputSource(
      type: OpenApiInputType.yamlString,
      source: content,
    );
  }

  /// Parses the OpenAPI specification from this input source.
  Future<OpenApiSpec> parse() async {
    switch (type) {
      case OpenApiInputType.file:
        return OpenApiParser.parseFromFile(source);
      case OpenApiInputType.url:
        return OpenApiParser.parseFromUrl(
          source,
          headers: headers,
          timeout: timeout,
        );
      case OpenApiInputType.jsonString:
        return OpenApiParser.parseFromJsonString(source);
      case OpenApiInputType.yamlString:
        return OpenApiParser.parseFromYamlString(source);
    }
  }
}

/// Types of OpenAPI input sources.
enum OpenApiInputType {
  /// Local file source.
  file,

  /// Remote URL source.
  url,

  /// JSON string content.
  jsonString,

  /// YAML string content.
  yamlString,
}
