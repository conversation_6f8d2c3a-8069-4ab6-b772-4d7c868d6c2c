/// Simplified OpenAPI specification data models for Phase 1.
///
/// This file contains minimal data structures for representing OpenAPI 3.0 specifications
/// focused on basic functionality without complex JSON serialization.
library;

/// Represents an OpenAPI 3.0 specification.
class OpenApiSpec {
  /// The OpenAPI version (e.g., "3.0.0").
  final String openapi;

  /// Metadata about the API.
  final Info info;

  /// The available paths and operations for the API.
  final Map<String, PathItem> paths;

  /// A list of tags used by the specification with additional metadata.
  final List<Tag>? tags;

  const OpenApiSpec({
    required this.openapi,
    required this.info,
    required this.paths,
    this.tags,
  });

  factory OpenApiSpec.fromJson(Map<String, dynamic> json) {
    final pathsJson = json['paths'] as Map<String, dynamic>? ?? {};
    final paths = <String, PathItem>{};
    
    pathsJson.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        paths[key] = PathItem.fromJson(value);
      }
    });

    return OpenApiSpec(
      openapi: json['openapi'] as String? ?? '3.0.0',
      info: Info.fromJson(json['info'] as Map<String, dynamic>? ?? {}),
      paths: paths,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => Tag.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'openapi': openapi,
      'info': info.toJson(),
      'paths': paths.map((key, value) => MapEntry(key, value.toJson())),
      if (tags != null) 'tags': tags!.map((e) => e.toJson()).toList(),
    };
  }

  /// Gets all unique tags from the specification.
  List<String> getAllTags() {
    final tagSet = <String>{};
    
    // Add tags from the global tags list
    if (tags != null) {
      tagSet.addAll(tags!.map((tag) => tag.name));
    }
    
    // Add tags from operations
    for (final pathItem in paths.values) {
      for (final operation in pathItem.getAllOperations()) {
        if (operation.tags != null) {
          tagSet.addAll(operation.tags!);
        }
      }
    }
    
    // Ensure we have at least one tag for operations without tags
    if (tagSet.isEmpty) {
      tagSet.add('default');
    }
    
    return tagSet.toList()..sort();
  }
}

/// Metadata about the API.
class Info {
  /// The title of the API.
  final String title;

  /// A short description of the API.
  final String? description;

  /// The version of the OpenAPI document.
  final String version;

  const Info({required this.title, this.description, required this.version});

  factory Info.fromJson(Map<String, dynamic> json) {
    return Info(
      title: json['title'] as String? ?? 'API',
      description: json['description'] as String?,
      version: json['version'] as String? ?? '1.0.0',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      if (description != null) 'description': description,
      'version': version,
    };
  }
}

/// Describes the operations available on a single path.
class PathItem {
  /// A definition of a GET operation on this path.
  final Operation? get;

  /// A definition of a PUT operation on this path.
  final Operation? put;

  /// A definition of a POST operation on this path.
  final Operation? post;

  /// A definition of a DELETE operation on this path.
  final Operation? delete;

  /// A definition of a OPTIONS operation on this path.
  final Operation? options;

  /// A definition of a HEAD operation on this path.
  final Operation? head;

  /// A definition of a PATCH operation on this path.
  final Operation? patch;

  const PathItem({
    this.get,
    this.put,
    this.post,
    this.delete,
    this.options,
    this.head,
    this.patch,
  });

  factory PathItem.fromJson(Map<String, dynamic> json) {
    return PathItem(
      get: json['get'] != null ? Operation.fromJson(json['get'] as Map<String, dynamic>) : null,
      put: json['put'] != null ? Operation.fromJson(json['put'] as Map<String, dynamic>) : null,
      post: json['post'] != null ? Operation.fromJson(json['post'] as Map<String, dynamic>) : null,
      delete: json['delete'] != null ? Operation.fromJson(json['delete'] as Map<String, dynamic>) : null,
      options: json['options'] != null ? Operation.fromJson(json['options'] as Map<String, dynamic>) : null,
      head: json['head'] != null ? Operation.fromJson(json['head'] as Map<String, dynamic>) : null,
      patch: json['patch'] != null ? Operation.fromJson(json['patch'] as Map<String, dynamic>) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (get != null) 'get': get!.toJson(),
      if (put != null) 'put': put!.toJson(),
      if (post != null) 'post': post!.toJson(),
      if (delete != null) 'delete': delete!.toJson(),
      if (options != null) 'options': options!.toJson(),
      if (head != null) 'head': head!.toJson(),
      if (patch != null) 'patch': patch!.toJson(),
    };
  }

  /// Gets all operations defined in this path item.
  List<Operation> getAllOperations() {
    return [
      if (get != null) get!,
      if (put != null) put!,
      if (post != null) post!,
      if (delete != null) delete!,
      if (options != null) options!,
      if (head != null) head!,
      if (patch != null) patch!,
    ];
  }
}

/// Describes a single API operation on a path.
class Operation {
  /// A list of tags for API documentation control.
  final List<String>? tags;

  /// A short summary of what the operation does.
  final String? summary;

  /// A verbose explanation of the operation behavior.
  final String? description;

  /// Unique string used to identify the operation.
  final String? operationId;

  /// A list of parameters that are applicable for this operation.
  final List<Parameter>? parameters;

  /// The list of possible responses as they are returned from executing this operation.
  final Map<String, Response> responses;

  const Operation({
    this.tags,
    this.summary,
    this.description,
    this.operationId,
    this.parameters,
    required this.responses,
  });

  factory Operation.fromJson(Map<String, dynamic> json) {
    final responsesJson = json['responses'] as Map<String, dynamic>? ?? {};
    final responses = <String, Response>{};
    
    responsesJson.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        responses[key] = Response.fromJson(value);
      }
    });

    return Operation(
      tags: (json['tags'] as List<dynamic>?)?.cast<String>(),
      summary: json['summary'] as String?,
      description: json['description'] as String?,
      operationId: json['operationId'] as String?,
      parameters: (json['parameters'] as List<dynamic>?)
          ?.map((e) => Parameter.fromJson(e as Map<String, dynamic>))
          .toList(),
      responses: responses,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (tags != null) 'tags': tags,
      if (summary != null) 'summary': summary,
      if (description != null) 'description': description,
      if (operationId != null) 'operationId': operationId,
      if (parameters != null) 'parameters': parameters!.map((e) => e.toJson()).toList(),
      'responses': responses.map((key, value) => MapEntry(key, value.toJson())),
    };
  }
}

/// Describes a single operation parameter.
class Parameter {
  /// The name of the parameter.
  final String name;

  /// The location of the parameter.
  final String location;

  /// A brief description of the parameter.
  final String? description;

  /// Determines whether this parameter is mandatory.
  final bool? required;

  const Parameter({
    required this.name,
    required this.location,
    this.description,
    this.required,
  });

  factory Parameter.fromJson(Map<String, dynamic> json) {
    return Parameter(
      name: json['name'] as String? ?? '',
      location: json['in'] as String? ?? 'query',
      description: json['description'] as String?,
      required: json['required'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'in': location,
      if (description != null) 'description': description,
      if (required != null) 'required': required,
    };
  }
}

/// Describes a single response from an API Operation.
class Response {
  /// A short description of the response.
  final String description;

  const Response({required this.description});

  factory Response.fromJson(Map<String, dynamic> json) {
    return Response(
      description: json['description'] as String? ?? 'Response',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'description': description,
    };
  }
}

/// Adds metadata to a single tag that is used by the Operation Object.
class Tag {
  /// The name of the tag.
  final String name;

  /// A short description for the tag.
  final String? description;

  const Tag({required this.name, this.description});

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      name: json['name'] as String? ?? '',
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      if (description != null) 'description': description,
    };
  }
}
