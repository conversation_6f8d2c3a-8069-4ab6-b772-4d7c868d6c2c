/// API client generator for hierarchical OpenAPI client structure.
///
/// This file contains the generator for creating the main API client class
/// and tag-based endpoint groupings with Dio integration.
library;

import 'package:code_builder/code_builder.dart';
import 'package:dart_style/dart_style.dart';

import '../openapi/openapi_spec_simple.dart' as openapi;

/// Generator for the main API client and tag classes.
class ApiClientGenerator {
  /// The OpenAPI specification.
  final openapi.OpenApiSpec spec;

  /// The formatter for generated code.
  final DartFormatter _formatter = DartFormatter();

  /// Creates a new API client generator.
  const ApiClientGenerator(this.spec);

  /// Generates the main API client class.
  String generateApiClient() {
    final library = Library(
      (b) => b
        ..comments.addAll([
          '/// Generated API client for ${spec.info.title}.',
          '/// This file is auto-generated. Do not modify manually.',
        ])
        ..directives.addAll([
          Directive.import('package:dio/dio.dart'),
          Directive.import(
            'package:openapi_generator/src/runtime/api_client_base.dart',
          ),
          ..._generateTagImports(),
        ])
        ..body.add(_generateApiClientClass()),
    );

    final emitter = DartEmitter();
    final code = library.accept(emitter).toString();
    return _formatter.format(code);
  }

  /// Generates all tag classes.
  Map<String, String> generateTagClasses() {
    final tagClasses = <String, String>{};
    final tags = spec.getAllTags();

    for (final tag in tags) {
      final tagCode = generateTagClass(tag);
      tagClasses[tag] = tagCode;
    }

    return tagClasses;
  }

  /// Generates a single tag class.
  String generateTagClass(String tagName) {
    final className = '${_toPascalCase(tagName)}Tag';
    final operations = _getOperationsForTag(tagName);

    final library = Library(
      (b) => b
        ..comments.addAll([
          '/// Generated tag class for $tagName operations.',
          '/// This file is auto-generated. Do not modify manually.',
        ])
        ..directives.addAll([
          Directive.import('package:dio/dio.dart'),
          Directive.import(
            'package:openapi_generator/src/runtime/api_client_base.dart',
          ),
          Directive.import(
            'package:openapi_generator/src/runtime/tag_base.dart',
          ),
          ..._generateModelImports(),
        ])
        ..body.add(_generateTagClass(className, tagName, operations)),
    );

    final emitter = DartEmitter();
    final code = library.accept(emitter).toString();
    return _formatter.format(code);
  }

  /// Generates import directives for tag classes.
  List<Directive> _generateTagImports() {
    final tags = spec.getAllTags();
    return tags.map((tag) {
      final fileName = _toSnakeCase(tag);
      return Directive.import('${fileName}_tag.dart');
    }).toList();
  }

  /// Generates import directives for model classes.
  List<Directive> _generateModelImports() {
    // For Phase 1, we'll skip model imports since we don't have schema generation yet
    return [];
  }

  /// Generates the main API client class.
  Class _generateApiClientClass() {
    final tags = spec.getAllTags();

    return Class(
      (b) => b
        ..name = 'ApiClient'
        ..extend = refer('ApiClientBase')
        ..docs.addAll([
          '/// Main API client for ${spec.info.title}.',
          '/// Provides hierarchical access to API endpoints through tag groupings.',
        ])
        ..fields.addAll(_generateTagFields(tags))
        ..constructors.add(_generateApiClientConstructor(tags))
        ..methods.addAll(_generateApiClientMethods()),
    );
  }

  /// Generates fields for tag instances.
  List<Field> _generateTagFields(List<String> tags) {
    return tags.map((tag) {
      final fieldName = _toCamelCase(tag);
      final className = '${_toPascalCase(tag)}Tag';

      return Field(
        (b) => b
          ..name = fieldName
          ..type = refer(className)
          ..modifier = FieldModifier.final$
          ..late = true
          ..docs.add('/// Access to $tag operations.'),
      );
    }).toList();
  }

  /// Generates the constructor for the API client.
  Constructor _generateApiClientConstructor(List<String> tags) {
    final initStatements = tags
        .map((tag) {
          final fieldName = _toCamelCase(tag);
          final className = '${_toPascalCase(tag)}Tag';
          return '$fieldName = $className(apiClient: this);';
        })
        .join('\n');

    return Constructor(
      (b) => b
        ..docs.addAll([
          '/// Creates a new API client instance.',
          '/// [baseUrl] The base URL for the API.',
          '/// [defaultHeaders] Default headers to include with all requests.',
          '/// [dioInstance] Optional custom Dio instance.',
        ])
        ..optionalParameters.addAll([
          Parameter(
            (b) => b
              ..name = 'baseUrl'
              ..type = refer('String')
              ..required = true
              ..named = true,
          ),
          Parameter(
            (b) => b
              ..name = 'defaultHeaders'
              ..type = refer('Map<String, String>')
              ..defaultTo = Code('const {}')
              ..named = true,
          ),
          Parameter(
            (b) => b
              ..name = 'dioInstance'
              ..type = refer('Dio?')
              ..named = true,
          ),
        ])
        ..initializers.add(
          Code(
            'super(baseUrl: baseUrl, defaultHeaders: defaultHeaders, dioInstance: dioInstance)',
          ),
        )
        ..body = Code(initStatements),
    );
  }

  /// Generates additional methods for the API client.
  List<Method> _generateApiClientMethods() {
    return [_generateGetInfoMethod()];
  }

  /// Generates a method to get API information.
  Method _generateGetInfoMethod() {
    return Method(
      (b) => b
        ..name = 'getApiInfo'
        ..returns = refer('Map<String, String>')
        ..docs.add('/// Returns information about this API.')
        ..body = Code('''
        return {
          'title': '${spec.info.title}',
          'version': '${spec.info.version}',
          'description': '${spec.info.description ?? ''}',
        };
      '''),
    );
  }

  /// Generates a tag class.
  Class _generateTagClass(
    String className,
    String tagName,
    List<OperationInfo> operations,
  ) {
    return Class(
      (b) => b
        ..name = className
        ..extend = refer('TagBase')
        ..docs.addAll([
          '/// Tag class for $tagName operations.',
          '/// Provides access to all endpoints tagged with "$tagName".',
        ])
        ..constructors.add(_generateTagConstructor(tagName))
        ..methods.addAll(_generateOperationMethods(operations)),
    );
  }

  /// Generates the constructor for a tag class.
  Constructor _generateTagConstructor(String tagName) {
    return Constructor(
      (b) => b
        ..docs.add('/// Creates a new $tagName tag instance.')
        ..optionalParameters.add(
          Parameter(
            (b) => b
              ..name = 'apiClient'
              ..type = refer('ApiClientBase')
              ..required = true
              ..named = true,
          ),
        )
        ..initializers.add(
          Code('super(apiClient: apiClient, tagName: \'$tagName\')'),
        ),
    );
  }

  /// Generates methods for operations in a tag.
  List<Method> _generateOperationMethods(List<OperationInfo> operations) {
    return operations.map(_generateOperationMethod).toList();
  }

  /// Generates a method for a single operation.
  Method _generateOperationMethod(OperationInfo operation) {
    final methodName = operation.operationId ?? _generateMethodName(operation);
    final returnType = _getReturnType(operation);
    final parameters = _generateMethodParameters(operation);

    return Method(
      (b) => b
        ..name = methodName
        ..returns = refer('Future<ApiResponse<$returnType>>')
        ..modifier = MethodModifier.async
        ..docs.addAll(_generateMethodDocs(operation))
        ..optionalParameters.addAll(parameters)
        ..body = _generateMethodBody(operation),
    );
  }

  /// Generates documentation for an operation method.
  List<String> _generateMethodDocs(OperationInfo operation) {
    final docs = <String>[];

    if (operation.summary != null) {
      docs.add('/// ${operation.summary}');
    }

    if (operation.description != null) {
      docs.add('/// ${operation.description}');
    }

    return docs;
  }

  /// Generates parameters for an operation method.
  List<Parameter> _generateMethodParameters(OperationInfo operation) {
    final parameters = <Parameter>[];

    // Add path parameters
    operation.pathParameters.forEach((param) {
      parameters.add(
        Parameter(
          (b) => b
            ..name = _toCamelCase(param.name)
            ..type = refer(_getParameterType(param))
            ..required = param.required ?? false
            ..named = true,
        ),
      );
    });

    // Add query parameters
    operation.queryParameters.forEach((param) {
      parameters.add(
        Parameter(
          (b) => b
            ..name = _toCamelCase(param.name)
            ..type = refer(_getParameterType(param))
            ..required = param.required ?? false
            ..named = true,
        ),
      );
    });

    // Request body handling will be added in Phase 2

    return parameters;
  }

  /// Generates the method body for an operation.
  Code _generateMethodBody(OperationInfo operation) {
    final pathParams = operation.pathParameters;
    final queryParams = operation.queryParameters;

    final pathParamsCode = pathParams.isNotEmpty
        ? 'final pathParams = {${pathParams.map((p) => '\'${p.name}\': ${_toCamelCase(p.name)}').join(', ')}};'
        : '';

    final queryParamsCode = queryParams.isNotEmpty
        ? 'final queryParams = buildQueryParameters({${queryParams.map((p) => '\'${p.name}\': ${_toCamelCase(p.name)}').join(', ')}});'
        : 'final queryParams = <String, dynamic>{};';

    final pathCode = pathParams.isNotEmpty
        ? 'final path = buildPath(\'${operation.path}\', pathParams);'
        : 'final path = \'${operation.path}\';';

    final methodCall =
        '''
      return ${operation.httpMethod.toLowerCase()}(
        path,
        queryParameters: queryParams,
        responseParser: (data) => data,
      );
    ''';

    return Code('''
      $pathParamsCode
      $queryParamsCode
      $pathCode
      $methodCall
    ''');
  }

  /// Gets operations for a specific tag.
  List<OperationInfo> _getOperationsForTag(String tagName) {
    final operations = <OperationInfo>[];

    spec.paths.forEach((path, pathItem) {
      final pathOperations = [
        if (pathItem.get != null) OperationInfo('GET', path, pathItem.get!),
        if (pathItem.post != null) OperationInfo('POST', path, pathItem.post!),
        if (pathItem.put != null) OperationInfo('PUT', path, pathItem.put!),
        if (pathItem.delete != null)
          OperationInfo('DELETE', path, pathItem.delete!),
        if (pathItem.patch != null)
          OperationInfo('PATCH', path, pathItem.patch!),
        if (pathItem.head != null) OperationInfo('HEAD', path, pathItem.head!),
        if (pathItem.options != null)
          OperationInfo('OPTIONS', path, pathItem.options!),
      ];

      for (final op in pathOperations) {
        if (op.operation.tags?.contains(tagName) ?? false) {
          operations.add(op);
        }
      }
    });

    return operations;
  }

  /// Gets the return type for an operation.
  String _getReturnType(OperationInfo operation) {
    // For now, return dynamic. In a full implementation, this would
    // analyze the response schema to determine the appropriate type.
    return 'dynamic';
  }

  /// Gets the Dart type for a parameter.
  String _getParameterType(openapi.Parameter parameter) {
    final isRequired = parameter.required ?? false;
    String baseType = 'String'; // Default type for Phase 1

    return isRequired ? baseType : '$baseType?';
  }

  /// Generates a method name from operation info.
  String _generateMethodName(OperationInfo operation) {
    final method = operation.httpMethod.toLowerCase();
    final pathSegments = operation.path
        .split('/')
        .where((segment) => segment.isNotEmpty && !segment.startsWith('{'))
        .map(_toCamelCase)
        .join('');

    return '$method$pathSegments';
  }

  /// Converts a string to camelCase.
  String _toCamelCase(String input) {
    if (input.isEmpty) return input;

    final words = input.split(RegExp(r'[_\-\s]+'));
    if (words.isEmpty) return input;

    final first = words.first.toLowerCase();
    final rest = words
        .skip(1)
        .map(
          (word) => word.isEmpty
              ? ''
              : word[0].toUpperCase() + word.substring(1).toLowerCase(),
        );

    return first + rest.join('');
  }

  /// Converts a string to PascalCase.
  String _toPascalCase(String input) {
    final camelCase = _toCamelCase(input);
    if (camelCase.isEmpty) return camelCase;
    return camelCase[0].toUpperCase() + camelCase.substring(1);
  }

  /// Converts a string to snake_case.
  String _toSnakeCase(String input) {
    return input
        .replaceAllMapped(
          RegExp(r'[A-Z]'),
          (match) => '_${match.group(0)!.toLowerCase()}',
        )
        .replaceAll(RegExp(r'^_'), '');
  }
}

/// Information about an operation for code generation.
class OperationInfo {
  /// The HTTP method.
  final String httpMethod;

  /// The path template.
  final String path;

  /// The operation definition.
  final openapi.Operation operation;

  /// Creates a new operation info.
  const OperationInfo(this.httpMethod, this.path, this.operation);

  /// Gets the operation ID.
  String? get operationId => operation.operationId;

  /// Gets the operation summary.
  String? get summary => operation.summary;

  /// Gets the operation description.
  String? get description => operation.description;

  /// Gets path parameters.
  List<openapi.Parameter> get pathParameters {
    return operation.parameters?.where((p) => p.location == 'path').toList() ??
        [];
  }

  /// Gets query parameters.
  List<openapi.Parameter> get queryParameters {
    return operation.parameters?.where((p) => p.location == 'query').toList() ??
        [];
  }
}
