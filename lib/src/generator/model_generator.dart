/// Model class generator for OpenAPI schemas.
///
/// This file contains the generator for creating Dart model classes
/// from OpenAPI schema definitions with JSON serialization support.
/// Phase 1 implementation - simplified for basic functionality.
library;

import '../openapi/openapi_spec_simple.dart' as openapi;

/// Generator for Dart model classes from OpenAPI schemas.
/// Phase 1 implementation - simplified for basic functionality.
class ModelGenerator {
  /// The OpenAPI specification.
  final openapi.OpenApiSpec spec;

  /// Creates a new model generator.
  const ModelGenerator(this.spec);

  /// Generates all model classes from the OpenAPI specification.
  /// Phase 1: Returns empty map since we don't have schema definitions yet.
  Map<String, String> generateModels() {
    // Phase 1: Skip model generation, focus on API client structure
    return <String, String>{};
  }
}

/// Utility class for model generation helpers.
/// Phase 1: Simplified implementation.
class ModelGeneratorUtils {
  /// Validates that a schema can be converted to a Dart model.
  /// Phase 1: Always returns false since we don't have schema support yet.
  static bool canGenerateModel(dynamic schema) {
    return false;
  }
}

  /// Generates a single model class from a schema.
  String generateModel(String className, Schema schema) {
    final library = Library(
      (b) => b
        ..comments.addAll([
          '/// Generated model class for $className.',
          '/// This file is auto-generated. Do not modify manually.',
        ])
        ..directives.addAll([
          Directive.import('package:json_annotation/json_annotation.dart'),
        ])
        ..body.addAll([
          _generatePartDirective(className),
          _generateModelClass(className, schema),
        ]),
    );

    final emitter = DartEmitter();
    final code = library.accept(emitter).toString();
    return _formatter.format(code);
  }

  /// Generates the part directive for the model file.
  Code _generatePartDirective(String className) {
    final fileName = _toSnakeCase(className);
    return Code("part '$fileName.g.dart';");
  }

  /// Generates the model class.
  Class _generateModelClass(String className, Schema schema) {
    return Class(
      (b) => b
        ..name = className
        ..annotations.add(refer('JsonSerializable').call([]))
        ..docs.addAll(_generateClassDocs(className, schema))
        ..fields.addAll(_generateFields(schema))
        ..constructors.add(_generateConstructor(className, schema))
        ..methods.addAll(_generateMethods(className, schema)),
    );
  }

  /// Generates documentation for the class.
  List<String> _generateClassDocs(String className, Schema schema) {
    final docs = <String>['/// $className model class.'];

    if (schema.type != null) {
      docs.add('/// Type: ${schema.type}');
    }

    return docs;
  }

  /// Generates fields for the model class.
  List<Field> _generateFields(Schema schema) {
    final fields = <Field>[];

    if (schema.properties != null) {
      schema.properties!.forEach((propertyName, propertySchema) {
        fields.add(_generateField(propertyName, propertySchema, schema));
      });
    }

    return fields;
  }

  /// Generates a single field.
  Field _generateField(
    String propertyName,
    Schema propertySchema,
    Schema parentSchema,
  ) {
    final isRequired = parentSchema.required?.contains(propertyName) ?? false;
    final dartType = _getDartType(propertySchema, !isRequired);

    return Field(
      (b) => b
        ..name = _toCamelCase(propertyName)
        ..type = refer(dartType)
        ..modifier = FieldModifier.final$
        ..annotations.addAll(_generateFieldAnnotations(propertyName))
        ..docs.addAll(_generateFieldDocs(propertyName, propertySchema)),
    );
  }

  /// Generates annotations for a field.
  List<Expression> _generateFieldAnnotations(String propertyName) {
    final annotations = <Expression>[];

    // Add JsonKey annotation if property name differs from field name
    final fieldName = _toCamelCase(propertyName);
    if (fieldName != propertyName) {
      annotations.add(
        refer('JsonKey').call([], {'name': literalString(propertyName)}),
      );
    }

    return annotations;
  }

  /// Generates documentation for a field.
  List<String> _generateFieldDocs(String propertyName, Schema propertySchema) {
    final docs = <String>['/// $propertyName field.'];

    if (propertySchema.type != null) {
      docs.add('/// Type: ${propertySchema.type}');
    }

    if (propertySchema.format != null) {
      docs.add('/// Format: ${propertySchema.format}');
    }

    return docs;
  }

  /// Generates the constructor for the model class.
  Constructor _generateConstructor(String className, Schema schema) {
    return Constructor(
      (b) => b
        ..constant = true
        ..optionalParameters.addAll(_generateConstructorParameters(schema)),
    );
  }

  /// Generates constructor parameters.
  List<Parameter> _generateConstructorParameters(Schema schema) {
    final parameters = <Parameter>[];

    if (schema.properties != null) {
      schema.properties!.forEach((propertyName, propertySchema) {
        final isRequired = schema.required?.contains(propertyName) ?? false;
        final fieldName = _toCamelCase(propertyName);

        parameters.add(
          Parameter(
            (b) => b
              ..name = fieldName
              ..toThis = true
              ..required = isRequired
              ..named = true,
          ),
        );
      });
    }

    return parameters;
  }

  /// Generates methods for the model class.
  List<Method> _generateMethods(String className, Schema schema) {
    return [
      _generateFromJsonMethod(className),
      _generateToJsonMethod(className),
    ];
  }

  /// Generates the fromJson factory method.
  Method _generateFromJsonMethod(String className) {
    return Method(
      (b) => b
        ..name = 'fromJson'
        ..static = true
        ..returns = refer(className)
        ..requiredParameters.add(
          Parameter(
            (b) => b
              ..name = 'json'
              ..type = refer('Map<String, dynamic>'),
          ),
        )
        ..body = Code('return _\$${className}FromJson(json);'),
    );
  }

  /// Generates the toJson method.
  Method _generateToJsonMethod(String className) {
    return Method(
      (b) => b
        ..name = 'toJson'
        ..returns = refer('Map<String, dynamic>')
        ..body = Code('return _\$${className}ToJson(this);'),
    );
  }

  /// Converts an OpenAPI schema type to a Dart type.
  String _getDartType(Schema schema, bool nullable) {
    String baseType;

    if (schema.ref != null) {
      // Reference to another schema
      baseType = _getClassNameFromRef(schema.ref!);
    } else {
      switch (schema.type) {
        case 'string':
          baseType = _getStringType(schema.format);
          break;
        case 'integer':
          baseType = 'int';
          break;
        case 'number':
          baseType = 'double';
          break;
        case 'boolean':
          baseType = 'bool';
          break;
        case 'array':
          final itemType = schema.items != null
              ? _getDartType(schema.items!, false)
              : 'dynamic';
          baseType = 'List<$itemType>';
          break;
        case 'object':
          if (schema.properties != null && schema.properties!.isNotEmpty) {
            // This should be a separate model class
            baseType = 'Map<String, dynamic>';
          } else {
            baseType = 'Map<String, dynamic>';
          }
          break;
        default:
          baseType = 'dynamic';
      }
    }

    return nullable ? '$baseType?' : baseType;
  }

  /// Gets the appropriate Dart type for string formats.
  String _getStringType(String? format) {
    switch (format) {
      case 'date':
      case 'date-time':
        return 'DateTime';
      case 'uri':
        return 'Uri';
      default:
        return 'String';
    }
  }

  /// Extracts class name from a schema reference.
  String _getClassNameFromRef(String ref) {
    // Extract the last part of the reference path
    final parts = ref.split('/');
    return parts.last;
  }

  /// Converts a string to camelCase.
  String _toCamelCase(String input) {
    if (input.isEmpty) return input;

    final words = input.split(RegExp(r'[_\-\s]+'));
    if (words.isEmpty) return input;

    final first = words.first.toLowerCase();
    final rest = words
        .skip(1)
        .map(
          (word) => word.isEmpty
              ? ''
              : word[0].toUpperCase() + word.substring(1).toLowerCase(),
        );

    return first + rest.join('');
  }

  /// Converts a string to snake_case.
  String _toSnakeCase(String input) {
    return input
        .replaceAllMapped(
          RegExp(r'[A-Z]'),
          (match) => '_${match.group(0)!.toLowerCase()}',
        )
        .replaceAll(RegExp(r'^_'), '');
  }
}

/// Utility class for model generation helpers.
class ModelGeneratorUtils {
  /// Validates that a schema can be converted to a Dart model.
  static bool canGenerateModel(Schema schema) {
    // Can generate models for object types with properties
    if (schema.type == 'object' && schema.properties != null) {
      return true;
    }

    // Can generate models for schemas with references
    if (schema.ref != null) {
      return true;
    }

    return false;
  }

  /// Gets all schema references from a specification.
  static Set<String> getAllSchemaReferences(OpenApiSpec spec) {
    final references = <String>{};

    void collectReferences(Schema schema) {
      if (schema.ref != null) {
        references.add(schema.ref!);
      }

      if (schema.properties != null) {
        schema.properties!.values.forEach(collectReferences);
      }

      if (schema.items != null) {
        collectReferences(schema.items!);
      }
    }

    // Collect from component schemas
    if (spec.components?.schemas != null) {
      spec.components!.schemas!.values.forEach(collectReferences);
    }

    // Collect from path operations
    spec.paths.values.forEach((pathItem) {
      pathItem.getAllOperations().forEach((operation) {
        // Collect from parameters
        operation.parameters?.forEach((parameter) {
          if (parameter.schema != null) {
            collectReferences(parameter.schema!);
          }
        });

        // Collect from request body
        operation.requestBody?.content.values.forEach((mediaType) {
          if (mediaType.schema != null) {
            collectReferences(mediaType.schema!);
          }
        });

        // Collect from responses
        operation.responses.values.forEach((response) {
          response.content?.values.forEach((mediaType) {
            if (mediaType.schema != null) {
              collectReferences(mediaType.schema!);
            }
          });
        });
      });
    });

    return references;
  }

  /// Determines the generation order for models based on dependencies.
  static List<String> getGenerationOrder(Map<String, Schema> schemas) {
    final order = <String>[];
    final visited = <String>{};
    final visiting = <String>{};

    void visit(String name) {
      if (visited.contains(name)) return;
      if (visiting.contains(name)) {
        // Circular dependency - add to order anyway
        return;
      }

      visiting.add(name);

      final schema = schemas[name];
      if (schema != null) {
        // Visit dependencies first
        _getDependencies(schema, schemas.keys.toSet()).forEach(visit);
      }

      visiting.remove(name);
      visited.add(name);
      order.add(name);
    }

    schemas.keys.forEach(visit);
    return order;
  }

  /// Gets the dependencies of a schema.
  static Set<String> _getDependencies(
    Schema schema,
    Set<String> availableSchemas,
  ) {
    final dependencies = <String>{};

    void collectDependencies(Schema s) {
      if (s.ref != null) {
        final refName = s.ref!.split('/').last;
        if (availableSchemas.contains(refName)) {
          dependencies.add(refName);
        }
      }

      if (s.properties != null) {
        s.properties!.values.forEach(collectDependencies);
      }

      if (s.items != null) {
        collectDependencies(s.items!);
      }
    }

    collectDependencies(schema);
    return dependencies;
  }
}
