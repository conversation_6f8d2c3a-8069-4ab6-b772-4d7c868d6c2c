/// Main code generator that orchestrates the generation process.
///
/// This file contains the main generator that coordinates model generation,
/// API client generation, and file output.
library;

import 'dart:io';
import 'package:path/path.dart' as path;

import '../openapi/openapi_spec_simple.dart';
import 'model_generator.dart';
import 'api_client_generator.dart';

/// Main code generator for OpenAPI specifications.
class CodeGenerator {
  /// The OpenAPI specification.
  final OpenApiSpec spec;

  /// The output directory for generated files.
  final String outputDirectory;

  /// The package name for generated code.
  final String packageName;

  /// Creates a new code generator.
  const CodeGenerator({
    required this.spec,
    required this.outputDirectory,
    this.packageName = 'generated_api',
  });

  /// Generates all code files from the OpenAPI specification.
  Future<GenerationResult> generate() async {
    final result = GenerationResult();

    try {
      // Create output directories
      await _createDirectories();

      // Generate models
      final modelGenerator = ModelGenerator(spec);
      final models = modelGenerator.generateModels();

      for (final entry in models.entries) {
        final fileName = '${_toSnakeCase(entry.key)}.dart';
        final filePath = path.join(outputDirectory, 'models', fileName);
        await _writeFile(filePath, entry.value);
        result.generatedFiles.add(filePath);
      }

      // Generate API client
      final apiClientGenerator = ApiClientGenerator(spec);

      // Generate main API client
      final apiClientCode = apiClientGenerator.generateApiClient();
      final apiClientPath = path.join(outputDirectory, 'api_client.dart');
      await _writeFile(apiClientPath, apiClientCode);
      result.generatedFiles.add(apiClientPath);

      // Generate tag classes
      final tagClasses = apiClientGenerator.generateTagClasses();
      for (final entry in tagClasses.entries) {
        final fileName = '${_toSnakeCase(entry.key)}_tag.dart';
        final filePath = path.join(outputDirectory, 'tags', fileName);
        await _writeFile(filePath, entry.value);
        result.generatedFiles.add(filePath);
      }

      // Generate barrel file
      final barrelCode = _generateBarrelFile(
        models.keys.toList(),
        spec.getAllTags(),
      );
      final barrelPath = path.join(outputDirectory, '$packageName.dart');
      await _writeFile(barrelPath, barrelCode);
      result.generatedFiles.add(barrelPath);

      // Generate pubspec.yaml for the generated package
      final pubspecCode = _generatePubspec();
      final pubspecPath = path.join(outputDirectory, 'pubspec.yaml');
      await _writeFile(pubspecPath, pubspecCode);
      result.generatedFiles.add(pubspecPath);

      result.success = true;
      result.message =
          'Successfully generated ${result.generatedFiles.length} files';
    } catch (e, stackTrace) {
      result.success = false;
      result.message = 'Generation failed: $e';
      result.error = e;
      result.stackTrace = stackTrace;
    }

    return result;
  }

  /// Creates the necessary output directories.
  Future<void> _createDirectories() async {
    final directories = [
      outputDirectory,
      path.join(outputDirectory, 'models'),
      path.join(outputDirectory, 'tags'),
    ];

    for (final dir in directories) {
      final directory = Directory(dir);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
    }
  }

  /// Writes content to a file.
  Future<void> _writeFile(String filePath, String content) async {
    final file = File(filePath);
    await file.writeAsString(content);
  }

  /// Generates a barrel file that exports all generated code.
  String _generateBarrelFile(List<String> modelNames, List<String> tagNames) {
    final buffer = StringBuffer();

    buffer.writeln('/// Generated API client library for ${spec.info.title}.');
    buffer.writeln('/// This file is auto-generated. Do not modify manually.');
    buffer.writeln('library $packageName;');
    buffer.writeln();

    // Export main API client
    buffer.writeln('// Main API client');
    buffer.writeln("export 'api_client.dart';");
    buffer.writeln();

    // Export models
    if (modelNames.isNotEmpty) {
      buffer.writeln('// Generated models');
      for (final modelName in modelNames) {
        final fileName = _toSnakeCase(modelName);
        buffer.writeln("export 'models/$fileName.dart';");
      }
      buffer.writeln();
    }

    // Export tag classes
    if (tagNames.isNotEmpty) {
      buffer.writeln('// Tag classes');
      for (final tagName in tagNames) {
        final fileName = _toSnakeCase(tagName);
        buffer.writeln("export 'tags/${fileName}_tag.dart';");
      }
      buffer.writeln();
    }

    return buffer.toString();
  }

  /// Generates a pubspec.yaml file for the generated package.
  String _generatePubspec() {
    return '''
name: $packageName
description: Generated API client for ${spec.info.title}
version: ${spec.info.version}

environment:
  sdk: ^3.0.0

dependencies:
  dio: ^5.0.0
  json_annotation: ^4.8.0
  openapi_generator:
    path: ../

dev_dependencies:
  build_runner: ^2.4.0
  json_serializable: ^6.7.0
  test: ^1.24.0
''';
  }

  /// Converts a string to snake_case.
  String _toSnakeCase(String input) {
    return input
        .replaceAllMapped(
          RegExp(r'[A-Z]'),
          (match) => '_${match.group(0)!.toLowerCase()}',
        )
        .replaceAll(RegExp(r'^_'), '');
  }
}

/// Result of the code generation process.
class GenerationResult {
  /// Whether the generation was successful.
  bool success = false;

  /// A message describing the result.
  String message = '';

  /// List of generated file paths.
  final List<String> generatedFiles = [];

  /// The error that occurred during generation, if any.
  dynamic error;

  /// The stack trace of the error, if any.
  StackTrace? stackTrace;

  /// Creates a new generation result.
  GenerationResult();

  /// Returns a summary of the generation result.
  String getSummary() {
    if (success) {
      return 'Generation completed successfully. Generated ${generatedFiles.length} files.';
    } else {
      return 'Generation failed: $message';
    }
  }

  /// Returns detailed information about the generation result.
  String getDetails() {
    final buffer = StringBuffer();

    buffer.writeln('Generation Result:');
    buffer.writeln('Success: $success');
    buffer.writeln('Message: $message');
    buffer.writeln('Generated Files: ${generatedFiles.length}');

    if (generatedFiles.isNotEmpty) {
      buffer.writeln('\nGenerated Files:');
      for (final file in generatedFiles) {
        buffer.writeln('  - $file');
      }
    }

    if (error != null) {
      buffer.writeln('\nError: $error');
      if (stackTrace != null) {
        buffer.writeln('Stack Trace:');
        buffer.writeln(stackTrace.toString());
      }
    }

    return buffer.toString();
  }
}

/// Configuration for code generation.
class GenerationConfig {
  /// The output directory for generated files.
  final String outputDirectory;

  /// The package name for generated code.
  final String packageName;

  /// Whether to generate documentation comments.
  final bool generateDocs;

  /// Whether to use null safety.
  final bool nullSafety;

  /// Custom naming conventions.
  final NamingConvention namingConvention;

  /// Creates a new generation configuration.
  const GenerationConfig({
    required this.outputDirectory,
    this.packageName = 'generated_api',
    this.generateDocs = true,
    this.nullSafety = true,
    this.namingConvention = const NamingConvention(),
  });

  /// Creates a copy of this config with updated values.
  GenerationConfig copyWith({
    String? outputDirectory,
    String? packageName,
    bool? generateDocs,
    bool? nullSafety,
    NamingConvention? namingConvention,
  }) {
    return GenerationConfig(
      outputDirectory: outputDirectory ?? this.outputDirectory,
      packageName: packageName ?? this.packageName,
      generateDocs: generateDocs ?? this.generateDocs,
      nullSafety: nullSafety ?? this.nullSafety,
      namingConvention: namingConvention ?? this.namingConvention,
    );
  }
}

/// Naming convention configuration.
class NamingConvention {
  /// Convention for class names.
  final String classNameConvention;

  /// Convention for method names.
  final String methodNameConvention;

  /// Convention for property names.
  final String propertyNameConvention;

  /// Convention for file names.
  final String fileNameConvention;

  /// Creates a new naming convention configuration.
  const NamingConvention({
    this.classNameConvention = 'PascalCase',
    this.methodNameConvention = 'camelCase',
    this.propertyNameConvention = 'camelCase',
    this.fileNameConvention = 'snake_case',
  });
}

/// Utility class for generation helpers.
class GenerationUtils {
  /// Validates the output directory.
  static Future<bool> validateOutputDirectory(String outputDirectory) async {
    try {
      final directory = Directory(outputDirectory);

      // Check if directory exists or can be created
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Check if directory is writable
      final testFile = File(path.join(outputDirectory, '.test_write'));
      await testFile.writeAsString('test');
      await testFile.delete();

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Cleans the output directory.
  static Future<void> cleanOutputDirectory(String outputDirectory) async {
    final directory = Directory(outputDirectory);
    if (await directory.exists()) {
      await directory.delete(recursive: true);
    }
    await directory.create(recursive: true);
  }

  /// Gets the relative path from one directory to another.
  static String getRelativePath(String from, String to) {
    return path.relative(to, from: from);
  }

  /// Validates that a package name is valid.
  static bool isValidPackageName(String packageName) {
    // Dart package names must be lowercase with underscores
    final regex = RegExp(r'^[a-z][a-z0-9_]*$');
    return regex.hasMatch(packageName);
  }
}
