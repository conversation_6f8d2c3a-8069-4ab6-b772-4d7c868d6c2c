name: openapi_generator
description: A comprehensive Dart package that automatically generates type-safe API client code from OpenAPI specifications.
version: 0.1.0
# repository: https://github.com/my_org/openapi_generator

environment:
  sdk: ^3.8.1

dependencies:
  # Core HTTP client
  dio: ^5.0.0

  # JSON serialization
  json_annotation: ^4.8.0

  # YAML parsing for OpenAPI specs
  yaml: ^3.1.0

  # HTTP client for fetching remote specs
  http: ^1.1.0

  # Path manipulation
  path: ^1.8.0

  # CLI argument parsing
  args: ^2.4.0

  # Code generation utilities
  code_builder: ^4.7.0
  dart_style: ^2.3.0

  # File system operations
  io: ^1.0.4

dev_dependencies:
  # Code generation
  build_runner: ^2.4.0
  json_serializable: ^6.7.0

  # Testing
  test: ^1.24.0
  mockito: ^5.4.0

  # Linting
  lints: ^5.0.0

executables:
  dart_openapi_gen: main
