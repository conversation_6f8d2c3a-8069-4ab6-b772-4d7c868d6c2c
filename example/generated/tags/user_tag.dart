// /// Generated tag class for user operations.
// /// This file is auto-generated. Do not modify manually.

import 'package:dio/dio.dart';
import 'package:openapi_generator/src/runtime/api_client_base.dart';
import 'package:openapi_generator/src/runtime/tag_base.dart';

/// Tag class for user operations.
/// Provides access to all endpoints tagged with "user".
class UserTag extends TagBase {
  /// Creates a new user tag instance.
  UserTag({required ApiClientBase apiClient})
      : super(apiClient: apiClient, tagName: 'user');

  /// Create user.
  /// This can only be done by the logged in user.
  Future<ApiResponse<dynamic>> createUser() async {
    final queryParams = <String, dynamic>{};
    final path = '/user';
    return post(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Creates list of users with given input array.
  /// Creates list of users with given input array.
  Future<ApiResponse<dynamic>> createUsersWithListInput() async {
    final queryParams = <String, dynamic>{};
    final path = '/user/createWithList';
    return post(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Logs user into the system.
  /// Log into the system.
  Future<ApiResponse<dynamic>> loginUser({
    String? username,
    String? password,
  }) async {
    final queryParams =
        buildQueryParameters({'username': username, 'password': password});
    final path = '/user/login';
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Logs out current logged in user session.
  /// Log user out of the system.
  Future<ApiResponse<dynamic>> logoutUser() async {
    final queryParams = <String, dynamic>{};
    final path = '/user/logout';
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Get user by user name.
  /// Get user detail based on username.
  Future<ApiResponse<dynamic>> getUserByName({required String username}) async {
    final pathParams = {'username': username};
    final queryParams = <String, dynamic>{};
    final path = buildPath('/user/{username}', pathParams);
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Update user resource.
  /// This can only be done by the logged in user.
  Future<ApiResponse<dynamic>> updateUser({required String username}) async {
    final pathParams = {'username': username};
    final queryParams = <String, dynamic>{};
    final path = buildPath('/user/{username}', pathParams);
    return put(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Delete user resource.
  /// This can only be done by the logged in user.
  Future<ApiResponse<dynamic>> deleteUser({required String username}) async {
    final pathParams = {'username': username};
    final queryParams = <String, dynamic>{};
    final path = buildPath('/user/{username}', pathParams);
    return delete(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }
}
