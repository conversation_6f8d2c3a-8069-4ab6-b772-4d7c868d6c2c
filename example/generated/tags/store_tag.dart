// /// Generated tag class for store operations.
// /// This file is auto-generated. Do not modify manually.

import 'package:dio/dio.dart';
import 'package:openapi_generator/src/runtime/api_client_base.dart';
import 'package:openapi_generator/src/runtime/tag_base.dart';

/// Tag class for store operations.
/// Provides access to all endpoints tagged with "store".
class StoreTag extends TagBase {
  /// Creates a new store tag instance.
  StoreTag({required ApiClientBase apiClient})
      : super(apiClient: apiClient, tagName: 'store');

  /// Returns pet inventories by status.
  /// Returns a map of status codes to quantities.
  Future<ApiResponse<dynamic>> getInventory() async {
    final queryParams = <String, dynamic>{};
    final path = '/store/inventory';
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Place an order for a pet.
  /// Place a new order in the store.
  Future<ApiResponse<dynamic>> placeOrder() async {
    final queryParams = <String, dynamic>{};
    final path = '/store/order';
    return post(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Find purchase order by ID.
  /// For valid response try integer IDs with value <= 5 or > 10. Other values will generate exceptions.
  Future<ApiResponse<dynamic>> getOrderById({required String orderid}) async {
    final pathParams = {'orderId': orderid};
    final queryParams = <String, dynamic>{};
    final path = buildPath('/store/order/{orderId}', pathParams);
    return get(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }

  /// Delete purchase order by identifier.
  /// For valid response try integer IDs with value < 1000. Anything above 1000 or non-integers will generate API errors.
  Future<ApiResponse<dynamic>> deleteOrder({required String orderid}) async {
    final pathParams = {'orderId': orderid};
    final queryParams = <String, dynamic>{};
    final path = buildPath('/store/order/{orderId}', pathParams);
    return delete(
      path,
      queryParameters: queryParams,
      responseParser: (data) => data,
    );
  }
}
