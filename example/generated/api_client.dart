// /// Generated API client for Swagger Petstore - OpenAPI 3.0.
// /// This file is auto-generated. Do not modify manually.

import 'package:dio/dio.dart';
import 'package:openapi_generator/src/runtime/api_client_base.dart';
import 'pet_tag.dart';
import 'store_tag.dart';
import 'user_tag.dart';

/// Main API client for Swagger Petstore - OpenAPI 3.0.
/// Provides hierarchical access to API endpoints through tag groupings.
class ApiClient extends ApiClientBase {
  /// Creates a new API client instance.
  /// [baseUrl] The base URL for the API.
  /// [defaultHeaders] Default headers to include with all requests.
  /// [dioInstance] Optional custom Dio instance.
  ApiClient({
    required String baseUrl,
    Map<String, String> defaultHeaders = const {},
    Dio? dioInstance,
  }) : super(
            baseUrl: baseUrl,
            defaultHeaders: defaultHeaders,
            dioInstance: dioInstance) {
    pet = PetTag(apiClient: this);
    store = StoreTag(apiClient: this);
    user = UserTag(apiClient: this);
  }

  /// Access to pet operations.
  final PetTag pet;

  /// Access to store operations.
  final StoreTag store;

  /// Access to user operations.
  final UserTag user;

  /// Returns information about this API.
  Map<String, String> getApiInfo() {
    return {
      'title': 'Swagger Petstore - OpenAPI 3.0',
      'version': '1.0.26',
      'description':
          'This is a sample Pet Store Server based on the OpenAPI 3.0 specification.  You can find out more about\nSwagger at [https://swagger.io](https://swagger.io). In the third iteration of the pet store, we\'ve switched to the design first approach!\nYou can now help us improve the API whether it\'s by making changes to the definition itself or to the code.\nThat way, with time, we can improve the API in general, and expose some of the new features in OAS3.\n\nSome useful links:\n- [The Pet Store repository](https://github.com/swagger-api/swagger-petstore)\n- [The source API definition for the Pet Store](https://github.com/swagger-api/swagger-petstore/blob/master/src/main/resources/openapi.yaml)',
    };
  }
}
