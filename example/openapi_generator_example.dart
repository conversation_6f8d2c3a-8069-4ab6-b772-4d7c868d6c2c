/// Example usage of the OpenAPI Generator.
///
/// This example demonstrates how to use the OpenAPI generator
/// to parse specifications and generate API client code.
library;

import 'package:openapi_generator/openapi_generator.dart';

Future<void> main() async {
  // Example 1: Parse OpenAPI spec from a local file
  await parseFromFileExample();

  // Example 2: Parse OpenAPI spec from a URL
  await parseFromUrlExample();

  // Example 3: Generate API client code
  await generateCodeExample();
}

/// Example of parsing an OpenAPI specification from a local file.
Future<void> parseFromFileExample() async {
  print('=== Parsing from local file ===');

  try {
    // This would parse a real OpenAPI spec file
    // final spec = await OpenApiParser.parseFromFile('./example/petstore.yaml');
    // print('Parsed API: ${spec.info.title} v${spec.info.version}');
    // print('Tags: ${spec.getAllTags().join(', ')}');

    print('Example: Would parse OpenAPI spec from local file');
  } catch (e) {
    print('Error parsing file: $e');
  }
}

/// Example of parsing an OpenAPI specification from a URL.
Future<void> parseFromUrlExample() async {
  print('\n=== Parsing from URL ===');

  try {
    // Example with the Petstore API
    final spec = await OpenApiParser.parseFromUrl(
      'https://petstore3.swagger.io/api/v3/openapi.json',
      timeout: Duration(seconds: 10),
    );

    print('Parsed API: ${spec.info.title} v${spec.info.version}');
    print('Description: ${spec.info.description ?? 'No description'}');
    print('Tags: ${spec.getAllTags().join(', ')}');
    print('Number of paths: ${spec.paths.length}');
  } catch (e) {
    print('Error parsing URL: $e');
  }
}

/// Example of generating API client code.
Future<void> generateCodeExample() async {
  print('\n=== Generating API client code ===');

  try {
    // Parse the Petstore API
    final spec = await OpenApiParser.parseFromUrl(
      'https://petstore3.swagger.io/api/v3/openapi.json',
    );

    // Generate code
    final generator = CodeGenerator(
      spec: spec,
      outputDirectory: './example/generated',
      packageName: 'petstore_api',
    );

    final result = await generator.generate();

    if (result.success) {
      print('✅ Code generation successful!');
      print('Generated ${result.generatedFiles.length} files:');
      for (final file in result.generatedFiles) {
        print('  📄 $file');
      }
    } else {
      print('❌ Code generation failed: ${result.message}');
    }
  } catch (e) {
    print('Error generating code: $e');
  }
}
